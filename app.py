import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
from datetime import datetime, timedelta

# Set page config
st.set_page_config(
    page_title="Epidemiology Research Dashboard",
    page_icon="🦠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Title and description
st.title("🦠 Epidemiology Research Dashboard")
st.markdown("A simple dashboard for epidemiological data analysis and visualization")

# Sidebar
st.sidebar.header("Dashboard Controls")

# Sample data generation function
@st.cache_data
def generate_sample_data():
    """Generate sample epidemiological data"""
    dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
    np.random.seed(42)
    
    # Generate synthetic disease data
    base_cases = 100
    trend = np.linspace(0, 50, len(dates))
    seasonal = 20 * np.sin(2 * np.pi * np.arange(len(dates)) / 365.25)
    noise = np.random.normal(0, 10, len(dates))
    
    cases = np.maximum(0, base_cases + trend + seasonal + noise).astype(int)
    
    # Create DataFrame
    df = pd.DataFrame({
        'date': dates,
        'cases': cases,
        'deaths': np.maximum(0, (cases * 0.02 + np.random.normal(0, 1, len(dates)))).astype(int),
        'recovered': np.maximum(0, (cases * 0.95 + np.random.normal(0, 5, len(dates)))).astype(int),
        'region': np.random.choice(['North', 'South', 'East', 'West'], len(dates))
    })
    
    return df

# Load data
data = generate_sample_data()

# Sidebar filters
st.sidebar.subheader("Filters")
date_range = st.sidebar.date_input(
    "Select Date Range",
    value=(data['date'].min(), data['date'].max()),
    min_value=data['date'].min(),
    max_value=data['date'].max()
)

selected_regions = st.sidebar.multiselect(
    "Select Regions",
    options=data['region'].unique(),
    default=data['region'].unique()
)

# Filter data based on selections
if len(date_range) == 2:
    start_date, end_date = date_range
    filtered_data = data[
        (data['date'] >= pd.to_datetime(start_date)) & 
        (data['date'] <= pd.to_datetime(end_date)) &
        (data['region'].isin(selected_regions))
    ]
else:
    filtered_data = data[data['region'].isin(selected_regions)]

# Main dashboard
col1, col2, col3, col4 = st.columns(4)

with col1:
    st.metric(
        label="Total Cases",
        value=f"{filtered_data['cases'].sum():,}",
        delta=f"{filtered_data['cases'].tail(7).sum() - filtered_data['cases'].tail(14).head(7).sum():,}"
    )

with col2:
    st.metric(
        label="Total Deaths",
        value=f"{filtered_data['deaths'].sum():,}",
        delta=f"{filtered_data['deaths'].tail(7).sum() - filtered_data['deaths'].tail(14).head(7).sum():,}"
    )

with col3:
    st.metric(
        label="Total Recovered",
        value=f"{filtered_data['recovered'].sum():,}",
        delta=f"{filtered_data['recovered'].tail(7).sum() - filtered_data['recovered'].tail(14).head(7).sum():,}"
    )

with col4:
    mortality_rate = (filtered_data['deaths'].sum() / filtered_data['cases'].sum() * 100) if filtered_data['cases'].sum() > 0 else 0
    st.metric(
        label="Mortality Rate",
        value=f"{mortality_rate:.2f}%"
    )

# Charts
st.subheader("📈 Time Series Analysis")

# Time series chart
fig_time = px.line(
    filtered_data.groupby('date').agg({
        'cases': 'sum',
        'deaths': 'sum',
        'recovered': 'sum'
    }).reset_index(),
    x='date',
    y=['cases', 'deaths', 'recovered'],
    title="Cases, Deaths, and Recoveries Over Time",
    labels={'value': 'Count', 'variable': 'Metric'}
)
st.plotly_chart(fig_time, use_container_width=True)

# Regional analysis
col1, col2 = st.columns(2)

with col1:
    st.subheader("🗺️ Regional Distribution")
    regional_data = filtered_data.groupby('region').agg({
        'cases': 'sum',
        'deaths': 'sum',
        'recovered': 'sum'
    }).reset_index()
    
    fig_region = px.bar(
        regional_data,
        x='region',
        y='cases',
        title="Total Cases by Region",
        color='cases',
        color_continuous_scale='Reds'
    )
    st.plotly_chart(fig_region, use_container_width=True)

with col2:
    st.subheader("📊 Case Distribution")
    fig_pie = px.pie(
        regional_data,
        values='cases',
        names='region',
        title="Cases Distribution by Region"
    )
    st.plotly_chart(fig_pie, use_container_width=True)

# Data table
st.subheader("📋 Raw Data")
if st.checkbox("Show raw data"):
    st.dataframe(
        filtered_data.sort_values('date', ascending=False),
        use_container_width=True
    )

# Download data
st.subheader("💾 Export Data")
csv = filtered_data.to_csv(index=False)
st.download_button(
    label="Download filtered data as CSV",
    data=csv,
    file_name=f"epidemiology_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
    mime="text/csv"
)

# Footer
st.markdown("---")
st.markdown("*This is a sample epidemiology research dashboard built with Streamlit*")
