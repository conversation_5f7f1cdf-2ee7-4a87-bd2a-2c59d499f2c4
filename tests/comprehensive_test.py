#!/usr/bin/env python3
"""
Comprehensive test script for the Disease-Agnostic Epidemiology Research Workflow.
Tests all phases with mock data to avoid API calls.
"""

import asyncio
import sys
from unittest.mock import Mock, AsyncMock, patch

def test_phase_0_user_input():
    """Test Phase 0: User Input Collection"""
    print("🔍 Testing Phase 0: User Input Collection...")
    
    try:
        from workflow import EpidemiologyResearchInput
        
        # Test valid input
        research_input = EpidemiologyResearchInput(
            disease_name="diabetes mellitus",
            min_publication_year=2020,
            max_publication_year=2024,
            max_articles=50
        )
        
        assert research_input.disease_name == "diabetes mellitus"
        assert research_input.min_publication_year == 2020
        assert research_input.max_publication_year == 2024
        assert research_input.max_articles == 50
        
        print("✅ Phase 0: User input validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Phase 0 failed: {e}")
        return False

def test_phase_1_data_models():
    """Test Phase 1: Data Models"""
    print("\n🔍 Testing Phase 1: Data Models...")
    
    try:
        from workflow import (
            PubMedArticle, 
            PubMedSearchResults,
            GeographicData,
            StudyMethodology
        )
        
        # Test PubMed Article
        article = PubMedArticle(
            pmid="12345678",
            title="Test Article on Diabetes Epidemiology",
            authors=["Smith J", "Doe A"],
            journal="Test Journal"
        )
        
        # Test Search Results
        search_results = PubMedSearchResults(
            query="diabetes epidemiology",
            total_found=100,
            articles=[article]
        )
        
        # Test Geographic Data
        geo_data = GeographicData(country="United States")
        
        # Test Study Methodology
        methodology = StudyMethodology(
            study_type="Cross-sectional",
            sample_size=1000
        )
        
        assert article.pmid == "12345678"
        assert search_results.total_found == 100
        assert geo_data.country == "United States"
        assert methodology.sample_size == 1000
        
        print("✅ Phase 1: Data models creation passed")
        return True
        
    except Exception as e:
        print(f"❌ Phase 1 failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_phase_2_caching():
    """Test Phase 2: Caching Functions"""
    print("\n🔍 Testing Phase 2: Caching Functions...")
    
    try:
        from workflow import (
            get_cached_pubmed_results,
            cache_pubmed_results,
            PubMedSearchResults,
            PubMedArticle
        )
        
        # Create mock workflow
        class MockWorkflow:
            def __init__(self):
                self.workflow_session_state = {}
        
        mock_workflow = MockWorkflow()
        
        # Test cache miss
        result = get_cached_pubmed_results(mock_workflow, "diabetes")
        assert result is None
        
        # Test cache storage and retrieval
        article = PubMedArticle(
            pmid="12345678",
            title="Test Article",
            authors=["Test Author"],
            journal="Test Journal"
        )
        
        search_results = PubMedSearchResults(
            query="diabetes",
            total_found=1,
            articles=[article]
        )
        
        cache_pubmed_results(mock_workflow, "diabetes", search_results)
        
        # Test cache hit
        cached_result = get_cached_pubmed_results(mock_workflow, "diabetes")
        assert cached_result is not None
        assert cached_result.query == "diabetes"
        
        print("✅ Phase 2: Caching functions passed")
        return True
        
    except Exception as e:
        print(f"❌ Phase 2 failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_phase_3_mock_workflow():
    """Test Phase 3: Mock Workflow Execution"""
    print("\n🔍 Testing Phase 3: Mock Workflow Execution...")
    
    try:
        from workflow import (
            epidemiology_research_execution,
            EpidemiologyResearchInput,
            PubMedSearchResults,
            PubMedArticle,
            ExtractedEpidemiologyData,
            GeographicData,
            StudyMethodology
        )
        
        # Create mock workflow
        class MockWorkflow:
            def __init__(self):
                self.workflow_session_state = {}
        
        mock_workflow = MockWorkflow()
        
        # Create test input
        research_input = EpidemiologyResearchInput(
            disease_name="diabetes",
            min_publication_year=2020,
            max_publication_year=2024,
            max_articles=5
        )
        
        # Mock the agents to avoid API calls
        with patch('workflow.pubmed_search_agent') as mock_search_agent, \
             patch('workflow.data_extraction_agent') as mock_extract_agent:
            
            # Mock PubMed search response
            mock_article = PubMedArticle(
                pmid="12345678",
                title="Diabetes Epidemiology Study",
                authors=["Test Author"],
                journal="Test Journal"
            )
            
            mock_search_results = PubMedSearchResults(
                query="diabetes",
                total_found=5,
                articles=[mock_article]
            )
            
            mock_search_response = Mock()
            mock_search_response.content = mock_search_results
            mock_search_agent.arun = AsyncMock(return_value=mock_search_response)
            
            # Mock data extraction response
            mock_extracted_data = ExtractedEpidemiologyData(
                article_pmid="12345678",
                geographic_data=GeographicData(country="United States"),
                study_methodology=StudyMethodology(
                    study_type="Cross-sectional",
                    sample_size=1000
                ),
                extraction_confidence=0.8
            )
            
            mock_extract_response = Mock()
            mock_extract_response.content = mock_extracted_data
            mock_extract_agent.arun = AsyncMock(return_value=mock_extract_response)
            
            # Mock utility functions
            with patch('workflow.generate_country_statistics') as mock_stats, \
                 patch('workflow.perform_demographic_analysis') as mock_demo, \
                 patch('workflow.calculate_temporal_trends') as mock_trends:
                
                mock_stats.return_value = []
                mock_demo.return_value = Mock()
                mock_trends.return_value = Mock()
                
                # Execute workflow
                result = await epidemiology_research_execution(
                    mock_workflow,
                    research_input=research_input,
                    use_search_cache=False,
                    use_extraction_cache=False,
                    use_report_cache=False
                )
                
                # Verify results
                assert result is not None
                assert hasattr(result, 'input_parameters')
                assert hasattr(result, 'search_results')
                assert hasattr(result, 'extracted_data')
                assert hasattr(result, 'final_report')
                
                print("✅ Phase 3: Mock workflow execution passed")
                return True
        
    except Exception as e:
        print(f"❌ Phase 3 failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_phase_4_utility_functions():
    """Test Phase 4: Utility Functions (if available)"""
    print("\n🔍 Testing Phase 4: Utility Functions...")
    
    try:
        try:
            from epidemiology_utils import (
                calculate_weighted_average,
                calculate_confidence_interval
            )
            
            # Test weighted average
            values = [10.0, 20.0, 30.0]
            weights = [1.0, 2.0, 3.0]
            result = calculate_weighted_average(values, weights)
            expected = (10*1 + 20*2 + 30*3) / (1+2+3)  # Should be 23.33...
            assert abs(result - expected) < 0.01
            
            # Test confidence interval
            test_values = [1.0, 2.0, 3.0, 4.0, 5.0]
            ci_lower, ci_upper = calculate_confidence_interval(test_values)
            assert ci_lower < ci_upper
            
            print("✅ Phase 4: Utility functions passed")
            return True
            
        except ImportError:
            print("⚠️  Phase 4: Utility functions not available (optional)")
            return True
            
    except Exception as e:
        print(f"❌ Phase 4 failed: {e}")
        return False

async def run_comprehensive_tests():
    """Run all comprehensive tests."""
    print("🧪 Running Comprehensive Epidemiology Workflow Tests")
    print("=" * 80)
    
    tests = [
        ("Phase 0: User Input Collection", test_phase_0_user_input),
        ("Phase 1: Data Models", test_phase_1_data_models),
        ("Phase 2: Caching Functions", test_phase_2_caching),
        ("Phase 3: Mock Workflow Execution", test_phase_3_mock_workflow),
        ("Phase 4: Utility Functions", test_phase_4_utility_functions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
                
            if result:
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 80)
    print(f"📊 COMPREHENSIVE TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All comprehensive tests passed! Workflow is fully functional.")
        return True
    elif passed >= total - 1:  # Allow 1 optional test to fail
        print("✅ Core functionality tests passed! Workflow is ready for use.")
        return True
    else:
        print("⚠️  Critical tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_comprehensive_tests())
    sys.exit(0 if success else 1)
