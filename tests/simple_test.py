#!/usr/bin/env python3
"""
Simple test script for basic functionality.
"""

def test_basic_imports():
    """Test basic imports."""
    try:
        print("Testing basic imports...")
        from workflow import EpidemiologyResearchInput
        print("✅ EpidemiologyResearchInput imported successfully")
        
        from workflow import PubMedArticle
        print("✅ PubMedArticle imported successfully")
        
        from workflow import epidemiology_research_workflow
        print("✅ epidemiology_research_workflow imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_model():
    """Test basic data model creation."""
    try:
        print("\nTesting data model creation...")
        from workflow import EpidemiologyResearchInput
        
        # Test valid input
        research_input = EpidemiologyResearchInput(
            disease_name="diabetes",
            min_publication_year=2020,
            max_publication_year=2024,
            max_articles=50
        )
        
        print(f"✅ Created research input: {research_input.disease_name}")
        return True
    except Exception as e:
        print(f"❌ Data model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_structure():
    """Test workflow structure."""
    try:
        print("\nTesting workflow structure...")
        from workflow import epidemiology_research_workflow
        
        print(f"✅ Workflow name: {epidemiology_research_workflow.name}")
        print(f"✅ Workflow has steps: {epidemiology_research_workflow.steps is not None}")
        return True
    except Exception as e:
        print(f"❌ Workflow structure test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 Running Simple Epidemiology Workflow Tests")
    print("=" * 60)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Data Model", test_data_model),
        ("Workflow Structure", test_workflow_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed!")
        return True
    else:
        print("⚠️  Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
