#!/usr/bin/env python3
"""
Simple validation script for the Disease-Agnostic Epidemiology Research Workflow.
This script tests basic functionality without external dependencies.
"""

import sys
import traceback
from datetime import datetime

def test_imports():
    """Test that all required modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        from workflow import (
            EpidemiologyResearchInput,
            PubMedArticle,
            PubMedSearchResults,
            ExtractedEpidemiologyData,
            GeographicData,
            StudyMethodology,
            EpidemiologyReport,
            WorkflowResults,
            collect_user_input,
            epidemiology_research_workflow
        )
        print("✅ Core workflow imports successful")
    except ImportError as e:
        print(f"❌ Core workflow import failed: {e}")
        return False
    
    try:
        from epidemiology_utils import (
            calculate_weighted_average,
            calculate_confidence_interval,
            perform_demographic_analysis,
            generate_country_statistics,
            export_to_excel,
            export_to_html,
            export_visualizations
        )
        print("✅ Utility functions imports successful")
    except ImportError as e:
        print(f"⚠️  Utility functions import failed (optional): {e}")
    
    return True

def test_data_models():
    """Test basic data model creation and validation."""
    print("\n🔍 Testing data models...")
    
    # Test EpidemiologyResearchInput
    try:
        from workflow import EpidemiologyResearchInput
        
        # Valid input
        valid_input = EpidemiologyResearchInput(
            disease_name="diabetes",
            min_publication_year=2020,
            max_publication_year=2024,
            max_articles=50
        )
        assert valid_input.disease_name == "diabetes"
        print("✅ EpidemiologyResearchInput validation passed")
        
        # Test invalid year
        try:
            invalid_input = EpidemiologyResearchInput(
                disease_name="diabetes",
                min_publication_year=1800,  # Too early
                max_publication_year=2024,
                max_articles=50
            )
            print("❌ Year validation should have failed")
            return False
        except ValueError:
            print("✅ Year validation working correctly")
            
    except Exception as e:
        print(f"❌ EpidemiologyResearchInput test failed: {e}")
        return False
    
    # Test PubMedArticle
    try:
        from workflow import PubMedArticle
        
        article = PubMedArticle(
            pmid="12345678",
            title="Test Article",
            authors=["Author A"],
            journal="Test Journal"
        )
        assert article.pmid == "12345678"
        print("✅ PubMedArticle creation passed")
        
    except Exception as e:
        print(f"❌ PubMedArticle test failed: {e}")
        return False
    
    # Test GeographicData
    try:
        from workflow import GeographicData
        
        geo_data = GeographicData(country="United States")
        assert geo_data.country == "United States"
        print("✅ GeographicData creation passed")
        
    except Exception as e:
        print(f"❌ GeographicData test failed: {e}")
        return False
    
    return True

def test_workflow_structure():
    """Test workflow structure and configuration."""
    print("\n🔍 Testing workflow structure...")
    
    try:
        from workflow import epidemiology_research_workflow
        
        # Check workflow properties
        assert epidemiology_research_workflow.name == "Disease-Agnostic Epidemiology Research Workflow v1.0"
        assert epidemiology_research_workflow.description is not None
        assert epidemiology_research_workflow.steps is not None
        
        print("✅ Workflow structure validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Workflow structure test failed: {e}")
        return False

def test_utility_functions():
    """Test utility functions if available."""
    print("\n🔍 Testing utility functions...")
    
    try:
        from epidemiology_utils import calculate_weighted_average, calculate_confidence_interval
        
        # Test weighted average
        values = [10.0, 20.0, 30.0]
        weights = [1.0, 2.0, 3.0]
        result = calculate_weighted_average(values, weights)
        expected = (10*1 + 20*2 + 30*3) / (1+2+3)  # Should be 23.33...
        assert abs(result - expected) < 0.01
        print("✅ Weighted average calculation passed")
        
        # Test confidence interval
        test_values = [1.0, 2.0, 3.0, 4.0, 5.0]
        ci_lower, ci_upper = calculate_confidence_interval(test_values)
        assert ci_lower < ci_upper
        print("✅ Confidence interval calculation passed")
        
        return True
        
    except ImportError:
        print("⚠️  Utility functions not available (optional)")
        return True
    except Exception as e:
        print(f"❌ Utility functions test failed: {e}")
        return False

def test_caching_functions():
    """Test caching functionality."""
    print("\n🔍 Testing caching functions...")
    
    try:
        from workflow import (
            get_cached_report, 
            cache_report,
            get_cached_pubmed_results,
            cache_pubmed_results
        )
        
        # Create mock workflow
        class MockWorkflow:
            def __init__(self):
                self.workflow_session_state = {}
        
        mock_workflow = MockWorkflow()
        
        # Test that cache miss returns None
        result = get_cached_report(mock_workflow, "test_disease")
        assert result is None
        print("✅ Cache miss handling passed")
        
        # Test that cache functions don't crash
        from workflow import EpidemiologyReport, ExecutiveSummary
        mock_report = EpidemiologyReport(
            executive_summary=ExecutiveSummary(
                disease_name="test",
                total_studies_analyzed=0,
                countries_covered=0,
                key_findings=[],
                data_quality_assessment="test"
            ),
            country_statistics=[],
            demographic_analysis=None,
            temporal_trends=None,
            clinical_implications=None,
            methodology_notes="test",
            citations=[]
        )
        
        cache_report(mock_workflow, "test_disease", mock_report)
        print("✅ Cache storage passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Caching functions test failed: {e}")
        traceback.print_exc()
        return False

def test_agent_definitions():
    """Test that agents are properly defined."""
    print("\n🔍 Testing agent definitions...")
    
    try:
        from workflow import (
            pubmed_search_agent,
            data_extraction_agent,
            report_generation_agent
        )
        
        # Check agents have required properties
        assert pubmed_search_agent.name is not None
        assert data_extraction_agent.name is not None
        assert report_generation_agent.name is not None
        
        print("✅ Agent definitions passed")
        return True
        
    except Exception as e:
        print(f"❌ Agent definitions test failed: {e}")
        return False

def run_all_tests():
    """Run all validation tests."""
    print("🧪 Starting Disease-Agnostic Epidemiology Research Workflow Validation")
    print("=" * 80)
    
    tests = [
        ("Import Tests", test_imports),
        ("Data Model Tests", test_data_models),
        ("Workflow Structure Tests", test_workflow_structure),
        ("Utility Function Tests", test_utility_functions),
        ("Caching Function Tests", test_caching_functions),
        ("Agent Definition Tests", test_agent_definitions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 80)
    print(f"📊 VALIDATION SUMMARY: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Workflow is ready for use.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
