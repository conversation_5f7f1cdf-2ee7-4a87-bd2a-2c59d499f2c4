"""
Simple tests for the Disease-Agnostic Epidemiology Research Workflow.
"""

from datetime import datetime

def test_data_model_validation():
    """Test basic data model validation."""
    from workflow import EpidemiologyResearchInput

    # Test valid input
    try:
        valid_input = EpidemiologyResearchInput(
            disease_name="diabetes",
            min_publication_year=2020,
            max_publication_year=2024,
            max_articles=50
        )
        print("✅ Valid input validation passed")
        assert valid_input.disease_name == "diabetes"
        assert valid_input.min_publication_year == 2020
    except Exception as e:
        print(f"❌ Valid input validation failed: {e}")
        return False

    # Test invalid year
    try:
        invalid_input = EpidemiologyResearchInput(
            disease_name="diabetes",
            min_publication_year=1800,  # Too early
            max_publication_year=2024,
            max_articles=50
        )
        print("❌ Invalid year validation should have failed")
        return False
    except ValueError:
        print("✅ Invalid year validation passed")
    except Exception as e:
        print(f"❌ Unexpected error in year validation: {e}")
        return False

    # Test invalid article count
    try:
        invalid_input = EpidemiologyResearchInput(
            disease_name="diabetes",
            min_publication_year=2020,
            max_publication_year=2024,
            max_articles=1000  # Too many
        )
        print("❌ Invalid article count validation should have failed")
        return False
    except ValueError:
        print("✅ Invalid article count validation passed")
    except Exception as e:
        print(f"❌ Unexpected error in article count validation: {e}")
        return False

    return True

def test_pubmed_article_creation():
    """Test PubMed article model creation."""
    from workflow import PubMedArticle

    try:
        article = PubMedArticle(
            pmid="12345678",
            title="Prevalence of diabetes mellitus in urban populations",
            authors=["Smith J", "Johnson A", "Brown K"],
            publication_date="2023-01-15",
            journal="Journal of Epidemiology",
            doi="10.1234/je.2023.001",
            abstract="This study examines the prevalence of diabetes mellitus...",
            full_text_url="https://example.com/article",
            keywords=["diabetes", "prevalence", "epidemiology"],
            mesh_terms=["Diabetes Mellitus", "Prevalence", "Epidemiology"]
        )

        assert article.pmid == "12345678"
        assert "diabetes" in article.title.lower()
        assert len(article.authors) == 3
        assert article.doi.startswith("10.")
        print("✅ PubMed article creation passed")
        return True
    except Exception as e:
        print(f"❌ PubMed article creation failed: {e}")
        return False

def test_extracted_data_creation():
    """Test extracted epidemiological data creation."""
    from workflow import (
        ExtractedEpidemiologyData,
        GeographicData,
        StudyMethodology,
        PrevalenceData,
        IncidenceData
    )

    try:
        extracted_data = ExtractedEpidemiologyData(
            article_pmid="12345678",
            geographic_data=GeographicData(
                country="United States"
            ),
            study_methodology=StudyMethodology(
                study_type="Cross-sectional",
                sample_size=5000
            ),
            extraction_confidence=0.85
        )

        assert extracted_data.article_pmid == "12345678"
        assert extracted_data.prevalence_data.point_prevalence == 8.5
        assert extracted_data.incidence_data.annual_incidence == 45.2
        assert extracted_data.geographic_data.country == "United States"
        assert extracted_data.extraction_confidence == 0.85
        print("✅ Extracted data creation passed")
        return True
    except Exception as e:
        print(f"❌ Extracted data creation failed: {e}")
        return False

# Unit Tests
class TestDataModels:
    """Test data model validation and functionality."""
    
    def test_epidemiology_research_input_validation(self):
        """Test input validation for research parameters."""
        # Valid input
        valid_input = EpidemiologyResearchInput(
            disease_name="diabetes",
            min_publication_year=2020,
            max_publication_year=2024,
            max_articles=50
        )
        assert valid_input.disease_name == "diabetes"
        assert valid_input.min_publication_year == 2020
        
        # Invalid year range
        with pytest.raises(ValueError):
            EpidemiologyResearchInput(
                disease_name="diabetes",
                min_publication_year=1800,  # Too early
                max_publication_year=2024,
                max_articles=50
            )
        
        # Invalid article count
        with pytest.raises(ValueError):
            EpidemiologyResearchInput(
                disease_name="diabetes",
                min_publication_year=2020,
                max_publication_year=2024,
                max_articles=1000  # Too many
            )
    
    def test_pubmed_article_creation(self, sample_pubmed_article):
        """Test PubMed article model creation."""
        assert sample_pubmed_article.pmid == "12345678"
        assert "diabetes" in sample_pubmed_article.title.lower()
        assert len(sample_pubmed_article.authors) == 3
        assert sample_pubmed_article.doi.startswith("10.")
    
    def test_extracted_data_validation(self, sample_extracted_data):
        """Test extracted epidemiological data validation."""
        assert sample_extracted_data.article_pmid == "12345678"
        assert sample_extracted_data.prevalence_data.point_prevalence == 8.5
        assert sample_extracted_data.incidence_data.annual_incidence == 45.2
        assert sample_extracted_data.geographic_data.country == "United States"
        assert sample_extracted_data.extraction_confidence == 0.85

class TestWorkflowFunctions:
    """Test individual workflow functions."""
    
    @patch('workflow.Prompt.ask')
    def test_collect_user_input(self, mock_prompt):
        """Test user input collection function."""
        mock_prompt.side_effect = ["diabetes", "2020", "2024", "50"]
        
        result = collect_user_input()
        
        assert result.disease_name == "diabetes"
        assert result.min_publication_year == 2020
        assert result.max_publication_year == 2024
        assert result.max_articles == 50
    
    @pytest.mark.asyncio
    async def test_pubmed_search_results_caching(self, sample_research_input):
        """Test PubMed search results caching functionality."""
        # Mock workflow with empty session state
        mock_workflow = Mock()
        mock_workflow.workflow_session_state = {}
        
        # Mock the PubMed search agent
        with patch('workflow.pubmed_search_agent') as mock_agent:
            mock_response = Mock()
            mock_response.content = PubMedSearchResults(
                query="diabetes mellitus",
                total_found=10,
                articles=[]
            )
            mock_agent.arun = AsyncMock(return_value=mock_response)
            
            # First call should hit the agent
            result1 = await get_pubmed_search_results(
                mock_workflow, sample_research_input, use_cache=True
            )
            
            # Second call should use cache
            result2 = await get_pubmed_search_results(
                mock_workflow, sample_research_input, use_cache=True
            )
            
            # Agent should only be called once
            assert mock_agent.arun.call_count == 1
            assert result1.query == result2.query
    
    @pytest.mark.asyncio
    async def test_data_extraction_error_handling(self, sample_search_results):
        """Test error handling in data extraction function."""
        mock_workflow = Mock()
        mock_workflow.workflow_session_state = {}
        
        # Mock the data extraction agent to raise an exception
        with patch('workflow.data_extraction_agent') as mock_agent:
            mock_agent.arun = AsyncMock(side_effect=Exception("API Error"))
            
            result = await extract_epidemiological_data(
                mock_workflow, "diabetes", sample_search_results, use_cache=False
            )
            
            # Should return empty list on error, not crash
            assert isinstance(result, list)
            assert len(result) == 0

class TestStatisticalAnalysis:
    """Test statistical analysis and forecasting functions."""
    
    def test_demographic_analysis(self, sample_extracted_data):
        """Test demographic analysis functionality."""
        from epidemiology_utils import perform_demographic_analysis
        
        # Create sample data with demographic breakdowns
        sample_extracted_data.demographic_breakdown = {
            "age_groups": {"18-44": 5.2, "45-64": 12.8, "65+": 18.5},
            "gender_breakdown": {"male": 9.1, "female": 7.9}
        }
        
        result = perform_demographic_analysis([sample_extracted_data])
        
        assert isinstance(result.age_stratified_rates, dict)
        assert isinstance(result.gender_differences, dict)
        assert len(result.key_insights) > 0
    
    def test_country_statistics_generation(self, sample_extracted_data):
        """Test country-level statistics generation."""
        from epidemiology_utils import generate_country_statistics
        
        result = generate_country_statistics([sample_extracted_data])
        
        assert len(result) == 1
        assert result[0].country == "United States"
        assert result[0].prevalence_percentage == 8.5
        assert result[0].incidence_rate_per_100k == 45.2

class TestExportFunctionality:
    """Test export and visualization functions."""
    
    def test_excel_export(self, tmp_path):
        """Test Excel export functionality."""
        from epidemiology_utils import export_to_excel
        
        # Create mock results
        mock_results = Mock(spec=WorkflowResults)
        mock_results.final_report = Mock()
        mock_results.final_report.executive_summary = Mock()
        mock_results.final_report.executive_summary.disease_name = "diabetes"
        mock_results.final_report.executive_summary.total_studies_analyzed = 10
        mock_results.final_report.executive_summary.countries_covered = 5
        mock_results.final_report.country_statistics = []
        mock_results.search_results = Mock()
        mock_results.search_results.articles = []
        mock_results.processing_time = 120.5
        
        output_path = tmp_path / "test_report.xlsx"
        
        result_path = export_to_excel(mock_results, str(output_path))
        
        assert Path(result_path).exists()
        assert result_path == str(output_path)
    
    def test_html_export(self, tmp_path):
        """Test HTML export functionality."""
        from epidemiology_utils import export_to_html
        
        # Create mock results
        mock_results = Mock(spec=WorkflowResults)
        mock_results.final_report = Mock()
        mock_results.final_report.executive_summary = Mock()
        mock_results.final_report.executive_summary.disease_name = "diabetes"
        mock_results.final_report.executive_summary.total_studies_analyzed = 10
        mock_results.final_report.executive_summary.countries_covered = 5
        mock_results.final_report.executive_summary.key_findings = ["Finding 1", "Finding 2"]
        mock_results.final_report.country_statistics = []
        mock_results.final_report.clinical_implications = Mock()
        mock_results.final_report.clinical_implications.healthcare_planning_insights = ["Insight 1"]
        mock_results.processing_time = 120.5
        
        output_path = tmp_path / "test_report.html"
        
        result_path = export_to_html(mock_results, str(output_path))
        
        assert Path(result_path).exists()
        assert result_path == str(output_path)
        
        # Check HTML content
        with open(result_path, 'r') as f:
            content = f.read()
            assert "diabetes" in content
            assert "Executive Summary" in content

class TestIntegrationWorkflow:
    """Integration tests for the complete workflow."""
    
    @pytest.mark.asyncio
    async def test_complete_workflow_execution(self, sample_research_input):
        """Test complete workflow execution with mocked components."""
        mock_workflow = Mock()
        mock_workflow.workflow_session_state = {}
        
        # Mock all agent responses
        with patch('workflow.pubmed_search_agent') as mock_search, \
             patch('workflow.data_extraction_agent') as mock_extract, \
             patch('workflow.generate_country_statistics') as mock_stats, \
             patch('workflow.perform_demographic_analysis') as mock_demo, \
             patch('workflow.calculate_temporal_trends') as mock_trends:
            
            # Setup mock responses
            mock_search.arun = AsyncMock(return_value=Mock(
                content=PubMedSearchResults(
                    query="diabetes",
                    total_found=5,
                    articles=[Mock(pmid="123", title="Test Article")]
                )
            ))
            
            mock_extract.arun = AsyncMock(return_value=Mock(
                content=Mock(spec=ExtractedEpidemiologyData)
            ))
            
            mock_stats.return_value = []
            mock_demo.return_value = Mock()
            mock_trends.return_value = Mock()
            
            # Execute workflow
            result = await epidemiology_research_execution(
                mock_workflow,
                research_input=sample_research_input,
                use_search_cache=False,
                use_extraction_cache=False,
                use_report_cache=False
            )
            
            # Verify result structure
            assert isinstance(result, WorkflowResults)
            assert result.input_parameters == sample_research_input
            assert result.processing_time > 0
            assert 0 <= result.success_rate <= 1

# Performance Tests
class TestPerformance:
    """Test workflow performance and scalability."""
    
    @pytest.mark.asyncio
    async def test_workflow_performance_with_large_dataset(self):
        """Test workflow performance with larger datasets."""
        # Create large mock dataset
        large_input = EpidemiologyResearchInput(
            disease_name="diabetes",
            min_publication_year=2020,
            max_publication_year=2024,
            max_articles=100
        )
        
        mock_workflow = Mock()
        mock_workflow.workflow_session_state = {}
        
        start_time = datetime.now()
        
        with patch('workflow.pubmed_search_agent') as mock_search, \
             patch('workflow.data_extraction_agent') as mock_extract:
            
            # Mock responses for large dataset
            mock_articles = [Mock(pmid=f"123{i}", title=f"Article {i}") for i in range(100)]
            mock_search.arun = AsyncMock(return_value=Mock(
                content=PubMedSearchResults(
                    query="diabetes",
                    total_found=100,
                    articles=mock_articles
                )
            ))
            
            mock_extract.arun = AsyncMock(return_value=Mock(
                content=Mock(spec=ExtractedEpidemiologyData)
            ))
            
            result = await epidemiology_research_execution(
                mock_workflow,
                research_input=large_input,
                use_search_cache=False,
                use_extraction_cache=False,
                use_report_cache=False
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Performance assertions
            assert execution_time < 300  # Should complete within 5 minutes
            assert isinstance(result, WorkflowResults)
            assert len(result.search_results.articles) == 100

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
