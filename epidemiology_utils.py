"""
Utility functions for epidemiological data analysis, visualization, and export.
"""

import pandas as pd
import numpy as np
from typing import List, Tuple
from pathlib import Path
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
import logging

from workflow import (
    ExtractedEpidemiologyData, 
    EpidemiologyReport, 
    CountryEpidemiologyStats,
    DemographicAnalysis,
    TemporalTrends,
    ExecutiveSummary,
    ClinicalImplications,
    WorkflowResults
)

logger = logging.getLogger(__name__)

# --- Statistical Analysis Functions ---

def calculate_weighted_average(values: List[float], weights: List[float]) -> float:
    """Calculate weighted average with confidence intervals."""
    if not values or not weights or len(values) != len(weights):
        return 0.0
    
    weighted_sum = sum(v * w for v, w in zip(values, weights))
    total_weight = sum(weights)
    
    return weighted_sum / total_weight if total_weight > 0 else 0.0

def calculate_confidence_interval(values: List[float], confidence_level: float = 0.95) -> <PERSON>ple[float, float]:
    """Calculate confidence interval for a list of values."""
    if len(values) < 2:
        return (0.0, 0.0)
    
    mean = np.mean(values)
    std_err = np.std(values) / np.sqrt(len(values))
    
    # Using normal approximation for confidence intervals
    # For small samples, this is an approximation - scipy would be more accurate
    z_value = 1.96  # 95% confidence interval
    margin_error = z_value * std_err
    
    return (mean - margin_error, mean + margin_error)

def perform_demographic_analysis(extracted_data: List[ExtractedEpidemiologyData]) -> DemographicAnalysis:
    """Perform comprehensive demographic analysis."""
    age_data = {}
    gender_data = {}
    ethnicity_data = {}
    socioeconomic_data = {}
    urban_rural_data = {}
    insights = []
    
    for data in extracted_data:
        if data.demographic_breakdown:
            # Aggregate age group data
            for age_group, rate in data.demographic_breakdown.age_groups.items():
                if age_group not in age_data:
                    age_data[age_group] = []
                age_data[age_group].append(rate)
            
            # Aggregate gender data
            for gender, rate in data.demographic_breakdown.gender_breakdown.items():
                if gender not in gender_data:
                    gender_data[gender] = []
                gender_data[gender].append(rate)
            
            # Aggregate other demographic data
            for ethnicity, rate in data.demographic_breakdown.ethnicity_breakdown.items():
                if ethnicity not in ethnicity_data:
                    ethnicity_data[ethnicity] = []
                ethnicity_data[ethnicity].append(rate)
    
    # Calculate averages
    age_averages = {group: np.mean(rates) for group, rates in age_data.items() if rates}
    gender_averages = {gender: np.mean(rates) for gender, rates in gender_data.items() if rates}
    ethnicity_averages = {ethnicity: np.mean(rates) for ethnicity, rates in ethnicity_data.items() if rates}
    
    # Generate insights
    if age_averages:
        max_age_group = max(age_averages.keys(), key=lambda k: age_averages[k])
        insights.append(f"Highest prevalence observed in age group: {max_age_group} ({age_averages[max_age_group]:.1f}%)")
    
    if gender_averages and len(gender_averages) >= 2:
        gender_diff = abs(gender_averages.get('male', 0) - gender_averages.get('female', 0))
        if gender_diff > 1.0:  # Significant difference
            higher_gender = 'male' if gender_averages.get('male', 0) > gender_averages.get('female', 0) else 'female'
            insights.append(f"Significant gender difference observed: {higher_gender} prevalence {gender_diff:.1f}% higher")
    
    return DemographicAnalysis(
        age_stratified_rates=age_averages,
        gender_differences=gender_averages,
        ethnicity_patterns=ethnicity_averages,
        socioeconomic_impact=socioeconomic_data,
        urban_rural_differences=urban_rural_data,
        key_insights=insights
    )

def calculate_temporal_trends(extracted_data: List[ExtractedEpidemiologyData]) -> TemporalTrends:
    """Calculate temporal trends and forecasting."""
    historical_incidence = {}
    historical_prevalence = {}
    
    # Extract temporal data (this would need more sophisticated parsing in real implementation)
    # For now, we'll create placeholder trends
    current_year = datetime.now().year
    for year in range(current_year - 10, current_year + 1):
        # Simulate trend data - in real implementation, extract from study dates
        historical_incidence[str(year)] = np.random.normal(50, 10)  # Placeholder
        historical_prevalence[str(year)] = np.random.normal(5, 1)   # Placeholder
    
    # Calculate trend direction
    recent_years = list(range(current_year - 5, current_year + 1))
    incidence_trend = np.polyfit(recent_years, [historical_incidence[str(y)] for y in recent_years], 1)[0]
    
    trend_direction = "increasing" if incidence_trend > 0.1 else "decreasing" if incidence_trend < -0.1 else "stable"
    annual_change_rate = incidence_trend
    
    # Generate forecasts
    forecasted_incidence = {}
    forecasted_prevalence = {}
    for year in range(current_year + 1, current_year + 11):
        forecasted_incidence[str(year)] = historical_incidence[str(current_year)] + (year - current_year) * annual_change_rate
        forecasted_prevalence[str(year)] = historical_prevalence[str(current_year)] * (1 + annual_change_rate/100)
    
    return TemporalTrends(
        historical_incidence=historical_incidence,
        historical_prevalence=historical_prevalence,
        trend_direction=trend_direction,
        annual_change_rate=annual_change_rate,
        forecasted_incidence=forecasted_incidence,
        forecasted_prevalence=forecasted_prevalence,
        confidence_intervals={},
        driving_factors=["Population aging", "Lifestyle changes", "Improved diagnostics"]
    )

def generate_country_statistics(extracted_data: List[ExtractedEpidemiologyData]) -> List[CountryEpidemiologyStats]:
    """Generate country-level epidemiological statistics."""
    country_data = {}
    
    for data in extracted_data:
        country = data.geographic_data.country
        if country not in country_data:
            country_data[country] = {
                'incidence_rates': [],
                'prevalence_rates': [],
                'populations': [],
                'source_studies': []
            }
        
        # Collect data
        if data.incidence_data and data.incidence_data.annual_incidence:
            country_data[country]['incidence_rates'].append(data.incidence_data.annual_incidence)
        
        if data.prevalence_data and data.prevalence_data.point_prevalence:
            country_data[country]['prevalence_rates'].append(data.prevalence_data.point_prevalence)
        
        if data.geographic_data.population_studied:
            country_data[country]['populations'].append(data.geographic_data.population_studied)
        
        country_data[country]['source_studies'].append(data.article_pmid)
    
    # Generate statistics for each country
    country_stats = []
    for country, data in country_data.items():
        incidence_rate = np.mean(data['incidence_rates']) if data['incidence_rates'] else None
        prevalence_rate = np.mean(data['prevalence_rates']) if data['prevalence_rates'] else None
        avg_population = np.mean(data['populations']) if data['populations'] else None
        
        # Calculate estimated patients (simplified calculation)
        diagnosed_patients = None
        if prevalence_rate and avg_population:
            diagnosed_patients = int(avg_population * prevalence_rate / 100)
        
        country_stats.append(CountryEpidemiologyStats(
            country=country,
            population=int(avg_population) if avg_population else None,
            incidence_rate_per_100k=incidence_rate,
            prevalence_percentage=prevalence_rate,
            diagnosed_patients=diagnosed_patients,
            treated_patients=int(diagnosed_patients * 0.7) if diagnosed_patients else None,  # Assume 70% treatment rate
            treatment_rate=70.0,  # Placeholder
            forecasted_patients_5yr=int(diagnosed_patients * 1.1) if diagnosed_patients else None,
            forecasted_patients_10yr=int(diagnosed_patients * 1.2) if diagnosed_patients else None,
            data_quality_score=8.0,  # Placeholder
            source_studies=data['source_studies']
        ))
    
    return country_stats

# --- Visualization Functions ---

def create_prevalence_map(country_stats: List[CountryEpidemiologyStats]) -> go.Figure:
    """Create world map showing prevalence rates by country."""
    countries = [stat.country for stat in country_stats]
    prevalence_rates = [stat.prevalence_percentage or 0 for stat in country_stats]
    
    fig = go.Figure(data=go.Choropleth(
        locations=countries,
        z=prevalence_rates,
        locationmode='country names',
        colorscale='Reds',
        colorbar_title="Prevalence (%)"
    ))
    
    fig.update_layout(
        title="Global Disease Prevalence by Country",
        geo=dict(showframe=False, showcoastlines=True)
    )
    
    return fig

def create_demographic_charts(demographic_analysis: DemographicAnalysis) -> go.Figure:
    """Create demographic analysis charts."""
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Age Groups', 'Gender Distribution', 'Ethnicity Patterns', 'Key Insights'),
        specs=[[{"type": "bar"}, {"type": "pie"}],
               [{"type": "bar"}, {"type": "table"}]]
    )
    
    # Age groups bar chart
    if demographic_analysis.age_stratified_rates:
        age_groups = list(demographic_analysis.age_stratified_rates.keys())
        age_rates = list(demographic_analysis.age_stratified_rates.values())
        fig.add_trace(go.Bar(x=age_groups, y=age_rates, name="Age Groups"), row=1, col=1)
    
    # Gender pie chart
    if demographic_analysis.gender_differences:
        genders = list(demographic_analysis.gender_differences.keys())
        gender_rates = list(demographic_analysis.gender_differences.values())
        fig.add_trace(go.Pie(labels=genders, values=gender_rates, name="Gender"), row=1, col=2)
    
    # Ethnicity bar chart
    if demographic_analysis.ethnicity_patterns:
        ethnicities = list(demographic_analysis.ethnicity_patterns.keys())
        ethnicity_rates = list(demographic_analysis.ethnicity_patterns.values())
        fig.add_trace(go.Bar(x=ethnicities, y=ethnicity_rates, name="Ethnicity"), row=2, col=1)
    
    fig.update_layout(height=800, showlegend=False, title_text="Demographic Analysis")
    return fig

def create_temporal_trends_chart(temporal_trends: TemporalTrends) -> go.Figure:
    """Create temporal trends and forecasting chart."""
    fig = go.Figure()
    
    # Historical data
    historical_years = list(temporal_trends.historical_incidence.keys())
    historical_rates = list(temporal_trends.historical_incidence.values())
    
    fig.add_trace(go.Scatter(
        x=historical_years,
        y=historical_rates,
        mode='lines+markers',
        name='Historical Incidence',
        line=dict(color='blue')
    ))
    
    # Forecasted data
    forecast_years = list(temporal_trends.forecasted_incidence.keys())
    forecast_rates = list(temporal_trends.forecasted_incidence.values())
    
    fig.add_trace(go.Scatter(
        x=forecast_years,
        y=forecast_rates,
        mode='lines+markers',
        name='Forecasted Incidence',
        line=dict(color='red', dash='dash')
    ))
    
    fig.update_layout(
        title="Temporal Trends and Forecasting",
        xaxis_title="Year",
        yaxis_title="Incidence Rate (per 100,000)",
        hovermode='x unified'
    )
    
    return fig

# --- Export Functions ---

def export_to_excel(results: WorkflowResults, output_path: str) -> str:
    """Export results to Excel file with multiple sheets."""
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        # Executive Summary
        summary_data = {
            'Metric': ['Disease', 'Studies Analyzed', 'Countries Covered', 'Global Prevalence (%)', 'Processing Time (s)'],
            'Value': [
                results.final_report.executive_summary.disease_name,
                results.final_report.executive_summary.total_studies_analyzed,
                results.final_report.executive_summary.countries_covered,
                results.final_report.executive_summary.global_prevalence_estimate or 'N/A',
                f"{results.processing_time:.1f}"
            ]
        }
        pd.DataFrame(summary_data).to_excel(writer, sheet_name='Executive Summary', index=False)
        
        # Country Statistics
        country_data = []
        for stat in results.final_report.country_statistics:
            country_data.append({
                'Country': stat.country,
                'Population': stat.population,
                'Incidence Rate (per 100k)': stat.incidence_rate_per_100k,
                'Prevalence (%)': stat.prevalence_percentage,
                'Diagnosed Patients': stat.diagnosed_patients,
                'Treated Patients': stat.treated_patients,
                'Treatment Rate (%)': stat.treatment_rate
            })
        
        if country_data:
            pd.DataFrame(country_data).to_excel(writer, sheet_name='Country Statistics', index=False)
        
        # Source Articles
        article_data = []
        for article in results.search_results.articles:
            article_data.append({
                'PMID': article.pmid,
                'Title': article.title,
                'Authors': ', '.join(article.authors),
                'Journal': article.journal,
                'Publication Date': article.publication_date,
                'DOI': article.doi
            })
        
        if article_data:
            pd.DataFrame(article_data).to_excel(writer, sheet_name='Source Articles', index=False)
    
    return output_path

def export_to_html(results: WorkflowResults, output_path: str) -> str:
    """Export results to HTML report."""
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Epidemiological Research Report: {disease_name}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            h1 {{ color: #2c3e50; }}
            h2 {{ color: #34495e; border-bottom: 2px solid #3498db; }}
            .summary {{ background-color: #ecf0f1; padding: 20px; border-radius: 5px; }}
            table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
            th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
            th {{ background-color: #3498db; color: white; }}
            .insight {{ background-color: #e8f6f3; padding: 10px; margin: 10px 0; border-left: 4px solid #1abc9c; }}
        </style>
    </head>
    <body>
        <h1>Epidemiological Research Report: {disease_name}</h1>
        
        <div class="summary">
            <h2>Executive Summary</h2>
            <p><strong>Studies Analyzed:</strong> {total_studies}</p>
            <p><strong>Countries Covered:</strong> {countries_covered}</p>
            <p><strong>Global Prevalence:</strong> {global_prevalence}%</p>
            <p><strong>Processing Time:</strong> {processing_time:.1f} seconds</p>
        </div>
        
        <h2>Key Findings</h2>
        <ul>
        {key_findings}
        </ul>
        
        <h2>Country Statistics</h2>
        {country_table}
        
        <h2>Clinical Implications</h2>
        {clinical_implications}
        
        <h2>Data Sources</h2>
        <p>This report is based on {total_studies} peer-reviewed studies retrieved from PubMed.</p>
        
        <footer>
            <p><em>Report generated on {generation_date}</em></p>
        </footer>
    </body>
    </html>
    """
    
    # Prepare data for template
    key_findings_html = '\n'.join([f'<li>{finding}</li>' for finding in results.final_report.executive_summary.key_findings])
    
    # Create country statistics table
    country_rows = []
    for stat in results.final_report.country_statistics:
        country_rows.append(f"""
        <tr>
            <td>{stat.country}</td>
            <td>{stat.prevalence_percentage or 'N/A'}</td>
            <td>{stat.incidence_rate_per_100k or 'N/A'}</td>
            <td>{stat.diagnosed_patients or 'N/A'}</td>
        </tr>
        """)
    
    country_table = f"""
    <table>
        <tr>
            <th>Country</th>
            <th>Prevalence (%)</th>
            <th>Incidence (per 100k)</th>
            <th>Diagnosed Patients</th>
        </tr>
        {''.join(country_rows)}
    </table>
    """
    
    clinical_implications_html = '\n'.join([
        f'<div class="insight">{implication}</div>' 
        for implication in results.final_report.clinical_implications.healthcare_planning_insights
    ])
    
    # Fill template
    html_content = html_template.format(
        disease_name=results.final_report.executive_summary.disease_name,
        total_studies=results.final_report.executive_summary.total_studies_analyzed,
        countries_covered=results.final_report.executive_summary.countries_covered,
        global_prevalence=results.final_report.executive_summary.global_prevalence_estimate or 'N/A',
        processing_time=results.processing_time or 0,
        key_findings=key_findings_html,
        country_table=country_table,
        clinical_implications=clinical_implications_html,
        generation_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    )
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return output_path

def export_visualizations(results: WorkflowResults, output_dir: str) -> List[str]:
    """Export all visualizations as HTML files."""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    exported_files = []
    
    # Prevalence map
    prevalence_map = create_prevalence_map(results.final_report.country_statistics)
    map_path = output_path / "prevalence_map.html"
    prevalence_map.write_html(str(map_path))
    exported_files.append(str(map_path))

    # Demographic charts
    demographic_chart = create_demographic_charts(results.final_report.demographic_analysis)
    demo_path = output_path / "demographic_analysis.html"
    demographic_chart.write_html(str(demo_path))
    exported_files.append(str(demo_path))

    # Temporal trends
    trends_chart = create_temporal_trends_chart(results.final_report.temporal_trends)
    trends_path = output_path / "temporal_trends.html"
    trends_chart.write_html(str(trends_path))
    exported_files.append(str(trends_path))
    
    return exported_files
