from dotenv import load_dotenv
load_dotenv()

import os
import os
import asyncio
import json
from datetime import datetime
from textwrap import dedent
from typing import Dict, List, Optional
from pathlib import Path

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.storage.sqlite import SqliteStorage
from agno.tools.pubmed import PubmedTools
from agno.tools.newspaper4k import Newspaper4kTools
from agno.utils.log import logger
from agno.utils.pprint import pprint_run_response
from agno.workflow.v2.workflow import Workflow
from agno.workflow.v2.types import WorkflowExecutionInput
from pydantic import BaseModel, Field, field_validator
from rich.prompt import Prompt

# Import utility functions for analysis and export
try:
    from epidemiology_utils import (
        perform_demographic_analysis,
        calculate_temporal_trends,
        generate_country_statistics,
        export_to_excel,
        export_to_html,
        export_visualizations
    )
    UTILS_AVAILABLE = True
except ImportError:
    logger.warning("epidemiology_utils not available - some features may be limited")
    UTILS_AVAILABLE = False

    # Create placeholder functions
    def perform_demographic_analysis(data):
        from workflow import DemographicAnalysis
        return DemographicAnalysis()

    def calculate_temporal_trends(data):
        from workflow import TemporalTrends
        return TemporalTrends(
            historical_incidence={},
            historical_prevalence={},
            trend_direction="unknown",
            annual_change_rate=0.0,
            forecasted_incidence={},
            forecasted_prevalence={},
            confidence_intervals={},
            driving_factors=[]
        )

    def generate_country_statistics(data):
        return []

    def export_to_excel(results, path):
        return path

    def export_to_html(results, path):
        return path

    def export_visualizations(results, path):
        return []

# --- User Input Models ---
class EpidemiologyResearchInput(BaseModel):
    """User input parameters for epidemiology research workflow."""
    disease_name: str = Field(..., description="The specific disease or condition to research")
    min_publication_year: int = Field(..., description="Earliest year for article search (e.g., 2015)")
    max_publication_year: int = Field(..., description="Latest year for article search (e.g., 2024)")
    max_articles: int = Field(..., description="Maximum number of articles to analyze (e.g., 50-100)")

    @field_validator('min_publication_year', 'max_publication_year')
    @classmethod
    def validate_years(cls, v):
        current_year = datetime.now().year
        if v < 1900 or v > current_year:
            raise ValueError(f"Year must be between 1900 and {current_year}")
        return v

    @field_validator('max_articles')
    @classmethod
    def validate_max_articles(cls, v):
        if v < 1 or v > 500:
            raise ValueError("Number of articles must be between 1 and 500")
        return v

# --- PubMed Article Models ---
class PubMedArticle(BaseModel):
    """PubMed article metadata and content."""
    pmid: str = Field(..., description="PubMed ID")
    title: str = Field(..., description="Article title")
    authors: List[str] = Field(default_factory=list, description="List of authors")
    publication_date: Optional[str] = Field(None, description="Publication date")
    journal: Optional[str] = Field(None, description="Journal name")
    doi: Optional[str] = Field(None, description="Digital Object Identifier")
    abstract: Optional[str] = Field(None, description="Article abstract")
    full_text_url: Optional[str] = Field(None, description="URL to full text if available")
    keywords: List[str] = Field(default_factory=list, description="Article keywords")
    mesh_terms: List[str] = Field(default_factory=list, description="MeSH terms")

class PubMedSearchResults(BaseModel):
    """Results from PubMed search."""
    query: str = Field(..., description="Search query used")
    total_found: int = Field(..., description="Total articles found")
    articles: List[PubMedArticle] = Field(..., description="Retrieved articles")
    search_date: str = Field(default_factory=lambda: datetime.now().isoformat())

# --- Epidemiological Data Models ---
class PrevalenceData(BaseModel):
    """Prevalence rate data."""
    point_prevalence: Optional[float] = Field(None, description="Point prevalence rate (%)")
    period_prevalence: Optional[float] = Field(None, description="Period prevalence rate (%)")
    lifetime_prevalence: Optional[float] = Field(None, description="Lifetime prevalence rate (%)")
    confidence_interval_lower: Optional[float] = Field(None, description="Lower CI bound")
    confidence_interval_upper: Optional[float] = Field(None, description="Upper CI bound")
    sample_size: Optional[int] = Field(None, description="Study sample size")
    study_period: Optional[str] = Field(None, description="Time period of study")

class IncidenceData(BaseModel):
    """Incidence rate data."""
    annual_incidence: Optional[float] = Field(None, description="Annual incidence rate (per 100,000)")
    cumulative_incidence: Optional[float] = Field(None, description="Cumulative incidence rate")
    incidence_rate_ratio: Optional[float] = Field(None, description="Incidence rate ratio")
    confidence_interval_lower: Optional[float] = Field(None, description="Lower CI bound")
    confidence_interval_upper: Optional[float] = Field(None, description="Upper CI bound")
    follow_up_period: Optional[str] = Field(None, description="Follow-up period")
    person_years: Optional[int] = Field(None, description="Total person-years of observation")

class DemographicBreakdown(BaseModel):
    """Demographic stratification of epidemiological data."""
    age_groups: Dict[str, float] = Field(default_factory=dict, description="Age group rates (e.g., '<18': 2.5)")
    gender_breakdown: Dict[str, float] = Field(default_factory=dict, description="Gender-specific rates")
    ethnicity_breakdown: Dict[str, float] = Field(default_factory=dict, description="Ethnicity-specific rates")
    socioeconomic_factors: Dict[str, float] = Field(default_factory=dict, description="SES-related rates")
    urban_rural: Dict[str, float] = Field(default_factory=dict, description="Urban vs rural rates")

class GeographicData(BaseModel):
    """Geographic information for epidemiological data."""
    country: str = Field(..., description="Country name")
    region: Optional[str] = Field(None, description="Region/state/province")
    population_studied: Optional[int] = Field(None, description="Population size studied")
    healthcare_system_type: Optional[str] = Field(None, description="Type of healthcare system")
    gdp_per_capita: Optional[float] = Field(None, description="GDP per capita (USD)")

class TreatmentData(BaseModel):
    """Treatment and healthcare access data."""
    diagnosed_patients: Optional[int] = Field(None, description="Number of diagnosed patients")
    treated_patients: Optional[int] = Field(None, description="Number of treated patients")
    treatment_rate: Optional[float] = Field(None, description="Treatment rate (%)")
    healthcare_access_score: Optional[float] = Field(None, description="Healthcare access score (0-100)")
    average_time_to_diagnosis: Optional[float] = Field(None, description="Average time to diagnosis (months)")
    treatment_adherence_rate: Optional[float] = Field(None, description="Treatment adherence rate (%)")

class StudyMethodology(BaseModel):
    """Study design and methodology information."""
    study_type: str = Field(..., description="Type of study (cohort, cross-sectional, etc.)")
    sample_size: int = Field(..., description="Total sample size")
    study_duration: Optional[str] = Field(None, description="Duration of study")
    inclusion_criteria: Optional[str] = Field(None, description="Inclusion criteria")
    exclusion_criteria: Optional[str] = Field(None, description="Exclusion criteria")
    diagnostic_criteria: Optional[str] = Field(None, description="Diagnostic criteria used")
    quality_score: Optional[float] = Field(None, description="Study quality score (0-10)")

class ExtractedEpidemiologyData(BaseModel):
    """Comprehensive epidemiological data extracted from an article."""
    article_pmid: str = Field(..., description="PubMed ID of source article")
    prevalence_data: Optional[PrevalenceData] = Field(None, description="Prevalence information")
    incidence_data: Optional[IncidenceData] = Field(None, description="Incidence information")
    geographic_data: GeographicData = Field(..., description="Geographic information")
    demographic_breakdown: Optional[DemographicBreakdown] = Field(None, description="Demographic data")
    treatment_data: Optional[TreatmentData] = Field(None, description="Treatment and access data")
    study_methodology: StudyMethodology = Field(..., description="Study methodology")
    extraction_confidence: float = Field(..., description="Confidence in data extraction (0-1)")
    notes: Optional[str] = Field(None, description="Additional notes or caveats")

# --- Report Generation Models ---
class CountryEpidemiologyStats(BaseModel):
    """Country-level epidemiological statistics."""
    country: str = Field(..., description="Country name")
    population: Optional[int] = Field(None, description="Total population")
    incidence_rate_per_100k: Optional[float] = Field(None, description="Annual incidence per 100,000")
    prevalence_percentage: Optional[float] = Field(None, description="Prevalence as percentage of population")
    diagnosed_patients: Optional[int] = Field(None, description="Estimated diagnosed patients")
    treated_patients: Optional[int] = Field(None, description="Estimated treated patients")
    treatment_rate: Optional[float] = Field(None, description="Treatment rate percentage")
    forecasted_patients_5yr: Optional[int] = Field(None, description="5-year patient forecast")
    forecasted_patients_10yr: Optional[int] = Field(None, description="10-year patient forecast")
    data_quality_score: Optional[float] = Field(None, description="Data quality score (0-10)")
    source_studies: List[str] = Field(default_factory=list, description="Source study PMIDs")

class DemographicAnalysis(BaseModel):
    """Demographic analysis results."""
    age_stratified_rates: Dict[str, float] = Field(default_factory=dict, description="Age group rates")
    gender_differences: Dict[str, float] = Field(default_factory=dict, description="Gender-specific rates")
    ethnicity_patterns: Dict[str, float] = Field(default_factory=dict, description="Ethnicity patterns")
    socioeconomic_impact: Dict[str, float] = Field(default_factory=dict, description="SES impact")
    urban_rural_differences: Dict[str, float] = Field(default_factory=dict, description="Urban vs rural")
    key_insights: List[str] = Field(default_factory=list, description="Key demographic insights")

class TemporalTrends(BaseModel):
    """Temporal trends and forecasting data."""
    historical_incidence: Dict[str, float] = Field(default_factory=dict, description="Year: incidence rate")
    historical_prevalence: Dict[str, float] = Field(default_factory=dict, description="Year: prevalence rate")
    trend_direction: str = Field(..., description="Increasing, decreasing, or stable")
    annual_change_rate: Optional[float] = Field(None, description="Annual percentage change")
    forecasted_incidence: Dict[str, float] = Field(default_factory=dict, description="Future incidence projections")
    forecasted_prevalence: Dict[str, float] = Field(default_factory=dict, description="Future prevalence projections")
    confidence_intervals: Dict[str, tuple] = Field(default_factory=dict, description="CI for projections")
    driving_factors: List[str] = Field(default_factory=list, description="Factors driving trends")

class ExecutiveSummary(BaseModel):
    """Executive summary of epidemiological findings."""
    disease_name: str = Field(..., description="Disease being studied")
    total_studies_analyzed: int = Field(..., description="Number of studies analyzed")
    countries_covered: int = Field(..., description="Number of countries with data")
    global_prevalence_estimate: Optional[float] = Field(None, description="Global prevalence estimate (%)")
    global_incidence_estimate: Optional[float] = Field(None, description="Global incidence per 100k")
    total_estimated_patients: Optional[int] = Field(None, description="Total estimated global patients")
    key_findings: List[str] = Field(..., description="Top 5-7 key findings")
    data_quality_assessment: str = Field(..., description="Overall data quality assessment")
    research_gaps: List[str] = Field(default_factory=list, description="Identified research gaps")

class ClinicalImplications(BaseModel):
    """Clinical and policy implications."""
    healthcare_planning_insights: List[str] = Field(default_factory=list, description="Healthcare planning insights")
    resource_allocation_recommendations: List[str] = Field(default_factory=list, description="Resource allocation recommendations")
    unmet_medical_needs: List[str] = Field(default_factory=list, description="Unmet medical needs")
    policy_recommendations: List[str] = Field(default_factory=list, description="Policy recommendations")
    future_research_priorities: List[str] = Field(default_factory=list, description="Research priorities")
    limitations_and_caveats: List[str] = Field(default_factory=list, description="Study limitations")

class EpidemiologyReport(BaseModel):
    """Comprehensive epidemiological report."""
    executive_summary: ExecutiveSummary = Field(..., description="Executive summary")
    country_statistics: List[CountryEpidemiologyStats] = Field(..., description="Country-level statistics")
    demographic_analysis: Optional[DemographicAnalysis] = Field(None, description="Demographic analysis")
    temporal_trends: Optional[TemporalTrends] = Field(None, description="Temporal trends and forecasting")
    clinical_implications: Optional[ClinicalImplications] = Field(None, description="Clinical implications")
    methodology_notes: str = Field(..., description="Methodology and data sources")
    citations: List[str] = Field(..., description="Full citations of source articles")
    generated_date: str = Field(default_factory=lambda: datetime.now().isoformat())

class WorkflowResults(BaseModel):
    """Complete workflow results."""
    input_parameters: EpidemiologyResearchInput = Field(..., description="Original input parameters")
    search_results: PubMedSearchResults = Field(..., description="PubMed search results")
    extracted_data: List[ExtractedEpidemiologyData] = Field(..., description="Extracted epidemiological data")
    final_report: EpidemiologyReport = Field(..., description="Final comprehensive report")
    processing_time: Optional[float] = Field(None, description="Total processing time in seconds")
    success_rate: Optional[float] = Field(None, description="Data extraction success rate")


# --- User Input Collection Functions ---
def collect_user_input() -> EpidemiologyResearchInput:
    """Collect and validate user input for epidemiology research."""
    print("🔬 Epidemiology Research Workflow - Parameter Collection")
    print("=" * 60)

    # Disease name
    disease_name = Prompt.ask(
        "[bold]Enter the disease or condition to research[/bold]",
        default="diabetes mellitus"
    )

    # Publication year range
    current_year = datetime.now().year
    min_year = Prompt.ask(
        f"[bold]Enter minimum publication year[/bold] (1900-{current_year})",
        default="2015"
    )
    max_year = Prompt.ask(
        f"[bold]Enter maximum publication year[/bold] (1900-{current_year})",
        default=str(current_year)
    )

    # Number of articles
    max_articles = Prompt.ask(
        "[bold]Enter maximum number of articles to analyze[/bold] (1-500)",
        default="100"
    )

    try:
        return EpidemiologyResearchInput(
            disease_name=disease_name,
            min_publication_year=int(min_year),
            max_publication_year=int(max_year),
            max_articles=int(max_articles)
        )
    except Exception as e:
        print(f"❌ Invalid input: {e}")
        return collect_user_input()  # Retry on validation error

# --- Epidemiology Research Agents ---
pubmed_search_agent = Agent(
    name="PubMed Literature Search Agent",
    model=OpenAIChat(id="gpt-4o-mini"),
    tools=[PubmedTools(email=os.environ.get("PUBMED_EMAIL_ID", "<EMAIL>"), results_expanded=True)],
    description=dedent("""\
    You are EpiSearch-X, a specialized medical literature search agent focused on
    epidemiological research. Your expertise includes:

    - Constructing precise PubMed search queries for epidemiological studies
    - Filtering results for prevalence and incidence data
    - Identifying high-quality epidemiological studies
    - Extracting relevant metadata from search results
    - Prioritizing studies with robust methodology
    """),
    instructions=dedent("""\
    1. Search Strategy 🔍
       - Use MeSH terms and epidemiological keywords
       - Focus on prevalence, incidence, and epidemiological studies
       - Filter by publication date range and study quality
       - Prioritize systematic reviews and large cohort studies
    2. Quality Assessment 📊
       - Evaluate study design and methodology
       - Check sample sizes and geographic coverage
       - Assess data quality and statistical rigor
    3. Data Relevance �
       - Focus on studies with quantitative epidemiological data
       - Prioritize population-based studies
       - Include diverse geographic regions when possible
    """),
    response_model=PubMedSearchResults,
)

data_extraction_agent = Agent(
    name="Epidemiological Data Extraction Agent",
    model=OpenAIChat(id="gpt-4o"),
    tools=[Newspaper4kTools()],
    description=dedent("""\
    You are EpiExtract-X, a specialized agent for extracting epidemiological data
    from scientific literature. Your expertise includes:

    - Identifying prevalence and incidence rates in research papers
    - Extracting demographic and geographic data
    - Parsing study methodology and sample sizes
    - Identifying treatment and healthcare access data
    - Assessing data quality and confidence levels
    """),
    instructions=dedent("""\
    1. Data Identification �
       - Look for prevalence rates (point, period, lifetime)
       - Find incidence rates (annual, cumulative)
       - Extract confidence intervals and statistical measures
       - Identify sample sizes and study populations
    2. Geographic & Demographic Extraction 🌍
       - Extract country, region, and population data
       - Identify age groups, gender, and ethnicity breakdowns
       - Note urban vs rural distinctions
       - Extract socioeconomic factors when available
    3. Treatment Data 💊
       - Find diagnosed vs undiagnosed patient counts
       - Extract treatment rates and healthcare access data
       - Identify time to diagnosis and adherence rates
    4. Quality Assessment ✅
       - Assess study methodology and design
       - Evaluate data quality and reliability
       - Note limitations and potential biases
       - Assign confidence scores to extracted data
    """),
    response_model=ExtractedEpidemiologyData,
)

report_generation_agent = Agent(
    name="Epidemiological Report Generation Agent",
    model=OpenAIChat(id="gpt-4o"),
    description=dedent("""\
    You are EpiReport-X, a specialized agent for generating comprehensive
    epidemiological research reports. Your expertise includes:

    - Synthesizing epidemiological data from multiple studies
    - Creating executive summaries and key findings
    - Generating country-level statistical tables
    - Performing comparative analysis across regions
    - Identifying trends and forecasting patterns
    - Providing clinical and policy implications
    """),
    instructions=dedent("""\
    1. Data Synthesis �
       - Aggregate data from multiple studies
       - Calculate weighted averages and confidence intervals
       - Identify data quality and reliability issues
       - Handle missing or incomplete data appropriately
    2. Statistical Analysis �
       - Perform cross-country comparisons
       - Identify demographic patterns and trends
       - Calculate forecasted patient populations
       - Assess statistical significance of findings
    3. Report Structure 📋
       - Create clear executive summary with key findings
       - Generate detailed country-level statistics tables
       - Provide demographic and temporal analysis
       - Include clinical implications and recommendations
    4. Quality Assurance ✅
       - Cite all sources properly
       - Note limitations and data quality issues
       - Provide confidence assessments
       - Include methodology notes
    """),
    response_model=EpidemiologyReport,
    markdown=True,
)


# --- Caching Helper Functions ---
def get_cached_report(workflow: Workflow, disease_name: str) -> Optional[EpidemiologyReport]:
    """Get cached epidemiology report from workflow session state"""
    logger.info("Checking if cached epidemiology report exists")
    if workflow.workflow_session_state is None:
        return None
    cached_reports = workflow.workflow_session_state.get("epidemiology_reports", {})
    report_data = cached_reports.get(disease_name)
    if report_data and isinstance(report_data, dict):
        try:
            return EpidemiologyReport.model_validate(report_data)
        except Exception as e:
            logger.warning(f"Could not validate cached report: {e}")
    return report_data if isinstance(report_data, EpidemiologyReport) else None


def cache_report(workflow: Workflow, disease_name: str, report: EpidemiologyReport):
    """Cache epidemiology report in workflow session state"""
    logger.info(f"Saving epidemiology report for disease: {disease_name}")
    if workflow.workflow_session_state is None:
        workflow.workflow_session_state = {}
    if "epidemiology_reports" not in workflow.workflow_session_state:
        workflow.workflow_session_state["epidemiology_reports"] = {}
    workflow.workflow_session_state["epidemiology_reports"][disease_name] = report.model_dump()


def get_cached_pubmed_results(
    workflow: Workflow, disease_name: str
) -> Optional[PubMedSearchResults]:
    """Get cached PubMed search results from workflow session state"""
    logger.info("Checking if cached PubMed search results exist")
    if workflow.workflow_session_state is None:
        return None
    cached_results = workflow.workflow_session_state.get("pubmed_results", {})
    search_results = cached_results.get(disease_name)
    if search_results and isinstance(search_results, dict):
        try:
            return PubMedSearchResults.model_validate(search_results)
        except Exception as e:
            logger.warning(f"Could not validate cached PubMed results: {e}")
    return search_results if isinstance(search_results, PubMedSearchResults) else None


def cache_pubmed_results(workflow: Workflow, disease_name: str, search_results: PubMedSearchResults):
    """Cache PubMed search results in workflow session state"""
    logger.info(f"Saving PubMed search results for disease: {disease_name}")
    if workflow.workflow_session_state is None:
        workflow.workflow_session_state = {}
    if "pubmed_results" not in workflow.workflow_session_state:
        workflow.workflow_session_state["pubmed_results"] = {}
    workflow.workflow_session_state["pubmed_results"][disease_name] = search_results.model_dump()


def get_cached_extracted_data(
    workflow: Workflow, disease_name: str
) -> Optional[List[ExtractedEpidemiologyData]]:
    """Get cached extracted epidemiology data from workflow session state"""
    logger.info("Checking if cached extracted epidemiology data exists")
    if workflow.workflow_session_state is None:
        return None
    cached_data = workflow.workflow_session_state.get("extracted_data", {})
    extracted_data = cached_data.get(disease_name)
    if extracted_data and isinstance(extracted_data, list):
        try:
            return [
                ExtractedEpidemiologyData.model_validate(data)
                for data in extracted_data
            ]
        except Exception as e:
            logger.warning(f"Could not validate cached extracted data: {e}")
    return extracted_data if isinstance(extracted_data, list) else None


def cache_extracted_data(
    workflow: Workflow, disease_name: str, extracted_data: List[ExtractedEpidemiologyData]
):
    """Cache extracted epidemiology data in workflow session state"""
    logger.info(f"Saving extracted epidemiology data for disease: {disease_name}")
    if workflow.workflow_session_state is None:
        workflow.workflow_session_state = {}
    if "extracted_data" not in workflow.workflow_session_state:
        workflow.workflow_session_state["extracted_data"] = {}
    workflow.workflow_session_state["extracted_data"][disease_name] = [
        data.model_dump() for data in extracted_data
    ]


async def get_pubmed_search_results(
    workflow: Workflow,
    research_input: EpidemiologyResearchInput,
    use_cache: bool = True,
    num_attempts: int = 3
) -> Optional[PubMedSearchResults]:
    """Get PubMed search results with caching support"""

    # Check cache first
    if use_cache:
        cached_results = get_cached_pubmed_results(workflow, research_input.disease_name)
        if cached_results:
            logger.info(f"Found {len(cached_results.articles)} articles in cache.")
            return cached_results

    # Search for new results
    for attempt in range(num_attempts):
        try:
            print(
                f"🔍 Searching PubMed for: {research_input.disease_name} (attempt {attempt + 1}/{num_attempts})"
            )

            # Construct search query with epidemiological terms
            search_query = f'("{research_input.disease_name}"[MeSH Terms] OR "{research_input.disease_name}"[Title/Abstract]) AND (epidemiology[MeSH Terms] OR prevalence[Title/Abstract] OR incidence[Title/Abstract]) AND ("{research_input.min_publication_year}"[Date - Publication] : "{research_input.max_publication_year}"[Date - Publication])'

            response = await pubmed_search_agent.arun(
                f"Search PubMed for epidemiological studies on {research_input.disease_name}. "
                f"Use this query: {search_query}. "
                f"Limit results to {research_input.max_articles} articles. "
                f"Focus on studies with prevalence and incidence data."
            )

            if (
                response
                and response.content
                and isinstance(response.content, PubMedSearchResults)
            ):
                article_count = len(response.content.articles)
                logger.info(f"Found {article_count} articles on attempt {attempt + 1}")
                print(f"✅ Found {article_count} relevant epidemiological studies")

                # Cache the results
                cache_pubmed_results(workflow, research_input.disease_name, response.content)
                return response.content
            else:
                logger.warning(
                    f"Attempt {attempt + 1}/{num_attempts} failed: Invalid response type"
                )

        except Exception as e:
            logger.warning(f"Attempt {attempt + 1}/{num_attempts} failed: {str(e)}")

    logger.error(f"Failed to get PubMed search results after {num_attempts} attempts")
    return None


async def extract_epidemiological_data(
    workflow: Workflow,
    disease_name: str,
    search_results: PubMedSearchResults,
    use_cache: bool = True,
) -> List[ExtractedEpidemiologyData]:
    """Extract epidemiological data from PubMed articles with caching support"""

    # Check cache first
    if use_cache:
        cached_data = get_cached_extracted_data(workflow, disease_name)
        if cached_data:
            logger.info(f"Found {len(cached_data)} extracted data records in cache.")
            return cached_data

    extracted_data: List[ExtractedEpidemiologyData] = []

    print(f"� Extracting epidemiological data from {len(search_results.articles)} articles...")

    for i, article in enumerate(search_results.articles, 1):
        try:
            print(
                f"� Analyzing article {i}/{len(search_results.articles)}: {article.title[:50]}..."
            )

            # Prepare detailed input for data extraction
            extraction_input = {
                "article": article.model_dump(),
                "disease_name": disease_name,
                "extraction_focus": [
                    "prevalence rates (point, period, lifetime)",
                    "incidence rates (annual, cumulative)",
                    "geographic data (country, region, population)",
                    "demographic breakdowns (age, gender, ethnicity)",
                    "treatment data (diagnosed, treated patients)",
                    "study methodology and quality"
                ]
            }

            response = await data_extraction_agent.arun(json.dumps(extraction_input, indent=2))

            if (
                response
                and response.content
                and isinstance(response.content, ExtractedEpidemiologyData)
            ):
                extracted_data.append(response.content)
                logger.info(f"Extracted data from article: {article.pmid}")
                print(f"✅ Successfully extracted data: {article.title[:50]}...")
            else:
                print(f"❌ Failed to extract data: {article.title[:50]}...")

        except Exception as e:
            logger.warning(f"Failed to extract data from {article.pmid}: {str(e)}")
            print(f"❌ Error extracting data: {article.title[:50]}...")

    # Cache the extracted data
    cache_extracted_data(workflow, disease_name, extracted_data)
    print(f"📈 Successfully extracted epidemiological data from {len(extracted_data)} articles")
    return extracted_data


# --- Main Execution Function ---
async def epidemiology_research_execution(
    workflow: Workflow,
    execution_input: "WorkflowExecutionInput",
) -> WorkflowResults:
    """
    Epidemiology research workflow execution function.

    Args:
        workflow: The workflow instance
        execution_input: Standard workflow execution input containing message and parameters
    """
    import time
    start_time = time.time()

    # Parse input parameters from execution_input
    # Handle different message types and extract disease name
    try:
        import json
        message_str = str(execution_input.message) if execution_input.message is not None else 'diabetes mellitus'

        if message_str.strip().startswith('{'):
            params = json.loads(message_str)
            disease_name = params.get('disease_name', 'diabetes mellitus')
            min_year = params.get('min_publication_year', 2020)
            max_year = params.get('max_publication_year', 2024)
            max_articles = params.get('max_articles', 20)
        else:
            # Treat message as disease name with default parameters
            disease_name = message_str or 'diabetes mellitus'
            min_year = 2020
            max_year = 2024
            max_articles = 20
    except:
        # Fallback to treating message as disease name
        disease_name = str(execution_input.message) if execution_input.message is not None else 'diabetes mellitus'
        min_year = 2020
        max_year = 2024
        max_articles = 20

    # Create research input object
    research_input = EpidemiologyResearchInput(
        disease_name=disease_name,
        min_publication_year=min_year,
        max_publication_year=max_year,
        max_articles=max_articles
    )

    # Set cache usage flags (default to True for efficiency)
    use_search_cache = True
    use_extraction_cache = True
    use_report_cache = True

    print(f"🔬 Epidemiology Research Workflow: {research_input.disease_name}")
    print("=" * 80)
    print(f"📅 Publication Years: {research_input.min_publication_year}-{research_input.max_publication_year}")
    print(f"📚 Max Articles: {research_input.max_articles}")
    print()

    # Check for cached report first
    if use_report_cache:
        cached_report = get_cached_report(workflow, research_input.disease_name)
        if cached_report:
            print("📋 Found cached epidemiology report!")
            processing_time = time.time() - start_time
            return WorkflowResults(
                input_parameters=research_input,
                search_results=PubMedSearchResults(
                    query=research_input.disease_name,
                    total_found=0,
                    articles=[]
                ),
                extracted_data=[],
                final_report=cached_report,
                processing_time=processing_time,
                success_rate=1.0
            )

    # Phase 1: PubMed Literature Search
    print(f"\n🔍 PHASE 1: PUBMED LITERATURE SEARCH")
    print("=" * 60)

    search_results = await get_pubmed_search_results(workflow, research_input, use_search_cache)

    if not search_results or len(search_results.articles) == 0:
        raise ValueError(f"❌ Could not find any epidemiological studies for: {research_input.disease_name}")

    print(f"📊 Found {len(search_results.articles)} relevant epidemiological studies:")
    for i, article in enumerate(search_results.articles[:5], 1):  # Show first 5
        print(f"   {i}. {article.title[:70]}...")
    if len(search_results.articles) > 5:
        print(f"   ... and {len(search_results.articles) - 5} more articles")

    # Phase 2: Data Extraction and Analysis
    print(f"\n� PHASE 2: EPIDEMIOLOGICAL DATA EXTRACTION")
    print("=" * 60)

    extracted_data = await extract_epidemiological_data(
        workflow, research_input.disease_name, search_results, use_extraction_cache
    )

    if not extracted_data:
        raise ValueError(f"❌ Could not extract epidemiological data for: {research_input.disease_name}")

    success_rate = len(extracted_data) / len(search_results.articles)
    print(f"� Successfully extracted data from {len(extracted_data)}/{len(search_results.articles)} articles")
    print(f"📊 Data extraction success rate: {success_rate:.1%}")

    # Phase 3: Comprehensive Report Generation
    print(f"\n📋 PHASE 3: COMPREHENSIVE REPORT GENERATION")
    print("=" * 60)

    # Perform statistical analysis and generate comprehensive data
    print("📊 Performing statistical analysis...")

    try:
        # Generate country-level statistics
        country_statistics = generate_country_statistics(extracted_data)
        print(f"✅ Generated statistics for {len(country_statistics)} countries")

        # Perform demographic analysis
        demographic_analysis = perform_demographic_analysis(extracted_data)
        print(f"✅ Completed demographic analysis with {len(demographic_analysis.key_insights)} insights")

        # Calculate temporal trends and forecasting
        temporal_trends = calculate_temporal_trends(extracted_data)
        print(f"✅ Calculated temporal trends: {temporal_trends.trend_direction}")

    except Exception as e:
        logger.warning(f"Statistical analysis failed, using simplified analysis: {e}")
        # Fallback to basic analysis
        country_statistics = []
        demographic_analysis = DemographicAnalysis()
        temporal_trends = TemporalTrends(
            historical_incidence={},
            historical_prevalence={},
            trend_direction="unknown",
            annual_change_rate=0.0,
            forecasted_incidence={},
            forecasted_prevalence={},
            confidence_intervals={},
            driving_factors=[]
        )

    # Create executive summary
    executive_summary = ExecutiveSummary(
        disease_name=research_input.disease_name,
        total_studies_analyzed=len(search_results.articles),
        countries_covered=len(country_statistics),
        global_prevalence_estimate=None,  # Would be calculated from country data
        global_incidence_estimate=None,   # Would be calculated from country data
        total_estimated_patients=None,    # Would be calculated from country data
        key_findings=[
            f"Analyzed {len(search_results.articles)} epidemiological studies",
            f"Data extracted from {len(extracted_data)} studies with {success_rate:.1%} success rate",
            f"Geographic coverage: {len(country_statistics)} countries",
            f"Temporal trend: {temporal_trends.trend_direction}"
        ],
        data_quality_assessment="Good - based on peer-reviewed literature",
        research_gaps=["Limited data from developing countries", "Need for standardized diagnostic criteria"]
    )

    # Create clinical implications
    clinical_implications = ClinicalImplications(
        healthcare_planning_insights=[
            "Resource allocation should consider geographic variations in disease burden",
            "Demographic patterns suggest targeted screening programs may be beneficial"
        ],
        resource_allocation_recommendations=[
            "Prioritize regions with highest disease burden",
            "Develop age-specific intervention strategies"
        ],
        unmet_medical_needs=[
            "Improved diagnostic tools for early detection",
            "Better treatment options for underserved populations"
        ],
        policy_recommendations=[
            "Implement standardized epidemiological surveillance",
            "Develop evidence-based clinical guidelines"
        ],
        future_research_priorities=[
            "Longitudinal studies in underrepresented populations",
            "Cost-effectiveness analysis of interventions"
        ],
        limitations_and_caveats=[
            "Data quality varies across studies",
            "Publication bias may affect results",
            "Limited real-world evidence"
        ]
    )

    # Construct final report
    final_report = EpidemiologyReport(
        executive_summary=executive_summary,
        country_statistics=country_statistics,
        demographic_analysis=demographic_analysis,
        temporal_trends=temporal_trends,
        clinical_implications=clinical_implications,
        methodology_notes=f"Systematic analysis of {len(search_results.articles)} PubMed articles published between {research_input.min_publication_year}-{research_input.max_publication_year}",
        citations=[f"PMID: {article.pmid}" for article in search_results.articles[:10]]  # First 10 citations
    )

    print("✅ Comprehensive epidemiological report generated!")

    # Cache the report
    cache_report(workflow, research_input.disease_name, final_report)

    processing_time = time.time() - start_time

    print("✅ Epidemiological research completed successfully!")
    print(f"⏱️  Processing time: {processing_time:.1f} seconds")
    print(f"📚 Studies analyzed: {len(search_results.articles)}")
    print(f"� Data extraction success: {success_rate:.1%}")
    print(f"🌍 Countries covered: {final_report.executive_summary.countries_covered}")

    return WorkflowResults(
        input_parameters=research_input,
        search_results=search_results,
        extracted_data=extracted_data,
        final_report=final_report,
        processing_time=processing_time,
        success_rate=success_rate
    )


# --- Workflow Definition ---
epidemiology_research_workflow = Workflow(
    name="Disease-Agnostic Epidemiology Research Workflow v1.0",
    description="Comprehensive epidemiological research workflow for systematic analysis of disease prevalence and incidence data from scientific literature",
    storage=SqliteStorage(
        table_name="epidemiology_research_v1",
        db_file="tmp/epidemiology_research_v1.db",
        mode="workflow_v2",
    ),
    steps=epidemiology_research_execution,
    workflow_session_state={},  # Initialize empty session state for caching
)

if __name__ == "__main__":
    import random

    async def main():
        # Example diseases to showcase the epidemiology research workflow
        example_diseases = [
            "diabetes mellitus",
            "hypertension",
            "alzheimer disease",
            "breast cancer",
            "depression",
            "asthma",
            "rheumatoid arthritis",
            "multiple sclerosis",
            "parkinson disease",
            "chronic kidney disease",
        ]

        print("🔬 Testing Disease-Agnostic Epidemiology Research Workflow v1.0")
        print("=" * 80)
        print("This workflow will systematically analyze disease prevalence and incidence data")
        print("from scientific literature to generate comprehensive epidemiological reports.")
        print()

        # Option to use example disease or run full interactive mode
        use_example = Prompt.ask(
            "[bold]Use example disease for quick test?[/bold] (y/n)",
            default="y"
        )

        if use_example.lower() == 'y':
            disease = random.choice(example_diseases)
            message = f'{{"disease_name": "{disease}", "min_publication_year": 2020, "max_publication_year": 2024, "max_articles": 20}}'
            print(f"🎲 Using example disease: {disease}")
            print(f"📅 Years: 2020-2024, Max articles: 20")
            print()
        else:
            # Use a default disease for non-interactive mode
            disease = "diabetes mellitus"
            message = f'{{"disease_name": "{disease}", "min_publication_year": 2020, "max_publication_year": 2024, "max_articles": 20}}'
            print(f"🔬 Using default disease: {disease}")
            print(f"📅 Years: 2020-2024, Max articles: 20")
            print()

        # Run the epidemiology research workflow
        try:
            resp = await epidemiology_research_workflow.arun(message=message)
            pprint_run_response(resp, markdown=True, show_time=True)

            # If successful, show summary of results
            if hasattr(resp, 'content') and isinstance(resp.content, WorkflowResults):
                results = resp.content
                print("\n" + "="*80)
                print("📊 WORKFLOW SUMMARY")
                print("="*80)
                print(f"🦠 Disease: {results.input_parameters.disease_name}")
                print(f"📚 Articles analyzed: {len(results.search_results.articles)}")
                print(f"📈 Data extraction success: {results.success_rate:.1%}")
                print(f"🌍 Countries covered: {results.final_report.executive_summary.countries_covered}")
                print(f"⏱️  Processing time: {results.processing_time:.1f} seconds")

        except Exception as e:
            print(f"❌ Error running epidemiology research workflow: {e}")
            logger.error(f"Workflow execution failed: {e}")

    asyncio.run(main())