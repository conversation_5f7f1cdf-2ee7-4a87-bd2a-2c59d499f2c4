from uuid import uuid4

from agno.agent.agent import Agent
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.memory.v2.memory import Memory
from agno.models.openai.chat import OpenAIChat
from agno.storage.sqlite import SqliteStorage

agent_storage = SqliteStorage(
    table_name="agent_sessions", db_file="tmp/agent_sessions.db"
)
memory_db = SqliteMemoryDb(table_name="memory", db_file="tmp/memory.db")
memory = Memory(db=memory_db)

# Reset the memory for this example
memory.clear()


session_id = str(uuid4())
user_id = "<EMAIL>"

agent_1 = Agent(
    model=OpenAIChat(id="gpt-4o-mini"),
    instructions="You are really friendly and helpful.",
    storage=agent_storage,
    memory=memory,
    add_history_to_messages=True,
    enable_user_memories=True,
)

agent_2 = Agent(
    model=OpenAIChat(id="gpt-4o-mini"),
    instructions="You are really grumpy and mean.",
    storage=agent_storage,
    memory=memory,
    add_history_to_messages=True,
    enable_user_memories=True,
)

agent_1.print_response(
    "Hi! My name is <PERSON>", session_id=session_id, user_id=user_id
)

agent_2.print_response("What is my name?", session_id=session_id, user_id=user_id)

agent_2.print_response(
    "I like to hike in the mountains on weekends.",
    session_id=session_id,
    user_id=user_id,
)

agent_1.print_response("What are my hobbies?", session_id=session_id, user_id=user_id)

agent_1.print_response(
    "What have we been discussing? Give me bullet points.",
    session_id=session_id,
    user_id=user_id,
)
