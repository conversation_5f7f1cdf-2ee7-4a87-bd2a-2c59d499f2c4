from agno.agent import Agent
from agno.models.anthropic import <PERSON>
from agno.storage.sqlite import SqliteStorage
from agno.tools.duckduckgo import DuckDuckGoTools
from rich.pretty import pprint

agent = Agent(
    # This session_id is usually auto-generated
    # But for this example, we can set it to a fixed value
    # This session will now forever continue as a very long chat
    session_id="agent_session_which_is_autogenerated_if_not_set",
    model=<PERSON>(id="claude-3-7-sonnet-latest"),
    storage=SqliteStorage(table_name="agent_sessions", db_file="tmp/agents.db"),
    tools=[DuckDuckGoTools()],
    add_history_to_messages=True,
    num_history_runs=3,
    add_datetime_to_instructions=True,
    markdown=True,
)

if __name__ == "__main__":
    print(f"Session id: {agent.session_id}")
    agent.print_response("How many people live in Canada?")
    agent.print_response("What is their national anthem?")
    agent.print_response("List my messages one by one")

    # Print all messages in this session
    messages_in_session = agent.get_messages_for_session()
    pprint(messages_in_session)
