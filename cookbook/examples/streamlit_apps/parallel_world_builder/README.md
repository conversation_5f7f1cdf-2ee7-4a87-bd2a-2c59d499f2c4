# Parallel World

This advanced example shows how to build a parallel world builder using Agno and imagination and creativity.

> Note: Fork and clone this repository if needed

### 1. Create a virtual environment

```shell
python3 -m venv .venv
source .venv/bin/activate
```

### 2. Install requirements

```shell
pip install -r cookbook/examples/streamlit_apps/parallel_world_builder/requirements.txt
```

### 3. Export `OPENAI_API_KEY`

```shell
export OPENAI_API_KEY=sk-***
```

Other API keys are optional, but if you'd like to test:

```shell
export ANTHROPIC_API_KEY=***
export GOOGLE_API_KEY=***
```

### 4. Run Streamlit App

```shell
streamlit run cookbook/examples/streamlit_apps/parallel_world_builder/app.py
```

- Open [localhost:8501](http://localhost:8501) to view the Parallel World Builder.

### 5. Message us on [discord](https://agno.link/discord) if you have any questions
