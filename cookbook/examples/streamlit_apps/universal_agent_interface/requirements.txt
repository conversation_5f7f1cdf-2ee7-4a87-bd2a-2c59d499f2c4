# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.7.12
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
aiofiles==24.1.0
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anthropic==0.64.0
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
anyio==4.10.0
    # via
    #   anthropic
    #   google-genai
    #   groq
    #   httpx
    #   openai
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
beautifulsoup4==4.13.4
    # via
    #   -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
    #   yfinance
blinker==1.9.0
    # via streamlit
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.8.3
    # via
    #   curl-cffi
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via curl-cffi
charset-normalizer==3.4.3
    # via requests
click==8.2.1
    # via
    #   ddgs
    #   streamlit
    #   typer
curl-cffi==0.13.0
    # via yfinance
ddgs==9.5.4
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
deprecation==2.1.0
    # via lancedb
distro==1.9.0
    # via
    #   anthropic
    #   groq
    #   openai
docstring-parser==0.17.0
    # via agno
duckdb==1.3.2
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
exa-py==1.15.2
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
frozendict==2.4.6
    # via yfinance
gitdb==4.0.12
    # via gitpython
gitpython==3.1.45
    # via
    #   agno
    #   streamlit
google-auth==2.40.3
    # via google-genai
google-genai==1.31.0
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
groq==0.31.0
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
grpcio==1.74.0
    # via qdrant-client
h11==0.16.0
    # via httpcore
h2==4.2.0
    # via httpx
hpack==4.1.0
    # via h2
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   agno
    #   anthropic
    #   exa-py
    #   google-genai
    #   groq
    #   openai
    #   qdrant-client
hyperframe==6.1.0
    # via h2
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jiter==0.10.0
    # via
    #   anthropic
    #   openai
jsonschema==4.25.1
    # via altair
jsonschema-specifications==2025.4.1
    # via jsonschema
lancedb==0.24.3
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
lxml==6.0.1
    # via
    #   ddgs
    #   python-docx
markdown-it-py==4.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
multitasking==0.0.12
    # via yfinance
narwhals==2.1.2
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
numpy==2.3.2
    # via
    #   lancedb
    #   pandas
    #   pydeck
    #   qdrant-client
    #   streamlit
    #   yfinance
openai==1.101.0
    # via
    #   -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
    #   exa-py
overrides==7.7.0
    # via lancedb
packaging==25.0
    # via
    #   agno
    #   altair
    #   deprecation
    #   lancedb
    #   streamlit
pandas==2.3.2
    # via
    #   streamlit
    #   yfinance
peewee==3.18.2
    # via yfinance
pillow==11.3.0
    # via streamlit
platformdirs==4.3.8
    # via yfinance
portalocker==3.2.0
    # via qdrant-client
primp==0.15.0
    # via ddgs
protobuf==6.32.0
    # via
    #   qdrant-client
    #   streamlit
    #   yfinance
pyarrow==21.0.0
    # via
    #   lancedb
    #   streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   agno
    #   anthropic
    #   exa-py
    #   google-genai
    #   groq
    #   lancedb
    #   openai
    #   pydantic-settings
    #   qdrant-client
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.2
    # via rich
pypdf==6.0.0
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
python-dateutil==2.9.0.post0
    # via pandas
python-docx==1.2.0
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
python-dotenv==1.1.1
    # via
    #   agno
    #   pydantic-settings
python-multipart==0.0.20
    # via agno
pytz==2025.2
    # via
    #   pandas
    #   yfinance
pyyaml==6.0.2
    # via agno
qdrant-client==1.15.1
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.5
    # via
    #   exa-py
    #   google-genai
    #   streamlit
    #   yfinance
rich==14.1.0
    # via
    #   agno
    #   typer
rpds-py==0.27.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   groq
    #   openai
soupsieve==2.7
    # via beautifulsoup4
sqlalchemy==2.0.43
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
streamlit==1.48.1
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
tantivy==0.24.0
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
tenacity==9.1.2
    # via
    #   google-genai
    #   streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.5.2
    # via streamlit
tqdm==4.67.1
    # via
    #   lancedb
    #   openai
typer==0.16.1
    # via agno
typing-extensions==4.14.1
    # via
    #   agno
    #   altair
    #   anthropic
    #   anyio
    #   beautifulsoup4
    #   exa-py
    #   google-genai
    #   groq
    #   openai
    #   pydantic
    #   pydantic-core
    #   python-docx
    #   referencing
    #   sqlalchemy
    #   streamlit
    #   typer
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.5.0
    # via
    #   qdrant-client
    #   requests
websockets==15.0.1
    # via
    #   google-genai
    #   yfinance
yfinance==0.2.65
    # via -r cookbook/examples/streamlit_apps/universal_agent_interface/requirements.in
