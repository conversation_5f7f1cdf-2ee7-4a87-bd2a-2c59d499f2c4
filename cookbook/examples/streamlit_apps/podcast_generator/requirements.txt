# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.7.12
    # via -r cookbook/examples/streamlit_apps/podcast_generator/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anyio==4.10.0
    # via
    #   httpx
    #   openai
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
blinker==1.9.0
    # via streamlit
cachetools==6.1.0
    # via streamlit
certifi==2025.8.3
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.3
    # via requests
click==8.2.1
    # via
    #   ddgs
    #   streamlit
    #   typer
ddgs==9.5.4
    # via -r cookbook/examples/streamlit_apps/podcast_generator/requirements.in
distro==1.9.0
    # via openai
docstring-parser==0.17.0
    # via agno
gitdb==4.0.12
    # via gitpython
gitpython==3.1.45
    # via
    #   agno
    #   streamlit
h11==0.16.0
    # via httpcore
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   agno
    #   openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jiter==0.10.0
    # via openai
jsonschema==4.25.1
    # via altair
jsonschema-specifications==2025.4.1
    # via jsonschema
lxml==6.0.1
    # via ddgs
markdown-it-py==4.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
narwhals==2.1.2
    # via altair
numpy==2.3.2
    # via
    #   pandas
    #   pydeck
    #   streamlit
openai==1.101.0
    # via -r cookbook/examples/streamlit_apps/podcast_generator/requirements.in
packaging==25.0
    # via
    #   agno
    #   altair
    #   streamlit
pandas==2.3.2
    # via streamlit
pillow==11.3.0
    # via streamlit
primp==0.15.0
    # via ddgs
protobuf==6.32.0
    # via streamlit
pyarrow==21.0.0
    # via streamlit
pydantic==2.11.7
    # via
    #   agno
    #   openai
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.2
    # via rich
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.1.1
    # via
    #   agno
    #   pydantic-settings
python-multipart==0.0.20
    # via agno
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via agno
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.5
    # via streamlit
rich==14.1.0
    # via
    #   agno
    #   typer
rpds-py==0.27.0
    # via
    #   jsonschema
    #   referencing
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anyio
    #   openai
streamlit==1.48.1
    # via -r cookbook/examples/streamlit_apps/podcast_generator/requirements.in
tenacity==9.1.2
    # via streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.5.2
    # via streamlit
tqdm==4.67.1
    # via openai
typer==0.16.1
    # via agno
typing-extensions==4.14.1
    # via
    #   agno
    #   altair
    #   anyio
    #   openai
    #   pydantic
    #   pydantic-core
    #   referencing
    #   streamlit
    #   typer
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.5.0
    # via requests
