# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.7.4
    # via -r cookbook/examples/streamlit_apps/deep_researcher/requirements.in
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.14
    # via scrapegraph-py
aiosignal==1.4.0
    # via aiohttp
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via httpx
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
beautifulsoup4==4.13.4
    # via scrapegraph-py
blinker==1.9.0
    # via streamlit
cachetools==6.1.0
    # via streamlit
certifi==2025.7.14
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   streamlit
    #   typer
docstring-parser==0.16
    # via agno
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   agno
    #   streamlit
h11==0.16.0
    # via httpcore
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via agno
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jsonschema==4.24.0
    # via altair
jsonschema-specifications==2025.4.1
    # via jsonschema
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
multidict==6.6.3
    # via
    #   aiohttp
    #   yarl
narwhals==1.47.0
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/examples/streamlit_apps/deep_researcher/requirements.in
numpy==2.3.1
    # via
    #   pandas
    #   pydeck
    #   streamlit
packaging==25.0
    # via
    #   altair
    #   streamlit
pandas==2.3.1
    # via streamlit
pillow==11.3.0
    # via streamlit
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
protobuf==6.31.1
    # via streamlit
pyarrow==20.0.0
    # via streamlit
pydantic==2.11.7
    # via
    #   agno
    #   pydantic-settings
    #   scrapegraph-py
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.2
    # via rich
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.1.1
    # via
    #   agno
    #   pydantic-settings
    #   scrapegraph-py
python-multipart==0.0.20
    # via agno
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via agno
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.4
    # via
    #   scrapegraph-py
    #   streamlit
rich==14.0.0
    # via
    #   agno
    #   typer
rpds-py==0.26.0
    # via
    #   jsonschema
    #   referencing
scrapegraph-py==1.14.2
    # via -r cookbook/examples/streamlit_apps/deep_researcher/requirements.in
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via anyio
soupsieve==2.7
    # via beautifulsoup4
streamlit==1.47.0
    # via -r cookbook/examples/streamlit_apps/deep_researcher/requirements.in
tenacity==9.1.2
    # via streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.5.1
    # via streamlit
typer==0.16.0
    # via agno
typing-extensions==4.14.1
    # via
    #   agno
    #   aiosignal
    #   altair
    #   anyio
    #   beautifulsoup4
    #   pydantic
    #   pydantic-core
    #   referencing
    #   streamlit
    #   typer
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.5.0
    # via requests
yarl==1.20.1
    # via aiohttp
