# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.7.12
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anyio==4.10.0
    # via
    #   google-genai
    #   httpx
    #   openai
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
blinker==1.9.0
    # via streamlit
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.8.3
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.3
    # via requests
click==8.2.1
    # via
    #   ddgs
    #   streamlit
    #   typer
ddgs==9.5.4
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
distro==1.9.0
    # via openai
docstring-parser==0.17.0
    # via agno
eval-type-backport==0.2.2
    # via mistralai
gitdb==4.0.12
    # via gitpython
gitpython==3.1.45
    # via
    #   agno
    #   streamlit
google-auth==2.40.3
    # via google-genai
google-genai==1.31.0
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
h11==0.16.0
    # via httpcore
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   agno
    #   google-genai
    #   mistralai
    #   openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
invoke==2.2.0
    # via mistralai
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jiter==0.10.0
    # via openai
jsonschema==4.25.1
    # via altair
jsonschema-specifications==2025.4.1
    # via jsonschema
lxml==6.0.1
    # via ddgs
markdown-it-py==4.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
mistralai==1.9.7
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
narwhals==2.1.2
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
numpy==2.3.2
    # via
    #   pandas
    #   pgvector
    #   pydeck
    #   streamlit
openai==1.101.0
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
packaging==25.0
    # via
    #   agno
    #   altair
    #   streamlit
pandas==2.3.2
    # via streamlit
pgvector==0.4.1
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
pillow==11.3.0
    # via streamlit
primp==0.15.0
    # via ddgs
protobuf==6.32.0
    # via streamlit
psycopg==3.2.9
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
psycopg-binary==3.2.9
    # via psycopg
pyarrow==21.0.0
    # via streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pydantic==2.11.7
    # via
    #   agno
    #   google-genai
    #   mistralai
    #   openai
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.2
    # via rich
python-dateutil==2.9.0.post0
    # via
    #   mistralai
    #   pandas
python-dotenv==1.1.1
    # via
    #   agno
    #   pydantic-settings
python-multipart==0.0.20
    # via agno
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   agno
    #   mistralai
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.5
    # via
    #   google-genai
    #   streamlit
rich==14.1.0
    # via
    #   agno
    #   typer
rpds-py==0.27.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
shellingham==1.5.4
    # via typer
simplejson==3.20.1
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anyio
    #   openai
sqlalchemy==2.0.43
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
streamlit==1.48.1
    # via -r cookbook/examples/streamlit_apps/vision_ai/requirements.in
tenacity==9.1.2
    # via
    #   google-genai
    #   streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.5.2
    # via streamlit
tqdm==4.67.1
    # via openai
typer==0.16.1
    # via agno
typing-extensions==4.14.1
    # via
    #   agno
    #   altair
    #   anyio
    #   google-genai
    #   openai
    #   psycopg
    #   pydantic
    #   pydantic-core
    #   referencing
    #   sqlalchemy
    #   streamlit
    #   typer
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   mistralai
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.5.0
    # via requests
websockets==15.0.1
    # via google-genai
