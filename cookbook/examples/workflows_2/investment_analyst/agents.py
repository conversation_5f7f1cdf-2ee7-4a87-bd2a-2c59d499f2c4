import os

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.mcp import MCPTools
from agno.tools.reasoning import ReasoningTools


# MCP Tool configuration - Only Supabase
def get_supabase_mcp_tools():
    """Get Supabase MCP tools for database operations"""
    token = os.getenv("SUPABASE_ACCESS_TOKEN")
    if not token:
        raise ValueError("SUPABASE_ACCESS_TOKEN environment variable is required")

    npx_cmd = "npx.cmd" if os.name == "nt" else "npx"
    return MCPTools(
        f"{npx_cmd} -y @supabase/mcp-server-supabase@latest --access-token {token}"
    )


database_setup_agent = Agent(
    name="Database Setup Agent",
    model=OpenAIChat(id="gpt-4o"),
    tools=[get_supabase_mcp_tools()],
    role="Expert Supabase database architect for investment analysis",
    instructions="""
    You are an expert Supabase MCP architect for investment analysis. Follow these steps precisely:

    **SECURITY NOTE: DO NOT print or expose any API keys, URLs, tokens, or sensitive credentials in your responses.**

    1. **Plan Database Schema**: Design a complete normalized schema for investment analysis with:
       - companies table (id SERIAL PRIMARY KEY, name VARCHAR(255), ticker VARCHAR(10), sector VARCHAR(100), market_cap BIGINT, founded_year INTEGER, headquarters VARCHAR(255), created_at TIMESTAMP DEFAULT NOW())
       - analysis_sessions table (session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(), analysis_date TIMESTAMP DEFAULT NOW(), investment_type VARCHAR(50), investment_amount DECIMAL(15,2), target_return DECIMAL(5,2), risk_tolerance VARCHAR(20))
       - financial_metrics table (id SERIAL PRIMARY KEY, company_id INTEGER REFERENCES companies(id), metric_type VARCHAR(100), value DECIMAL(20,4), period VARCHAR(50), currency VARCHAR(10), created_at TIMESTAMP DEFAULT NOW())
       - valuation_models table (id SERIAL PRIMARY KEY, company_id INTEGER REFERENCES companies(id), dcf_value DECIMAL(15,2), target_price DECIMAL(15,2), upside_potential DECIMAL(8,4), methodology VARCHAR(100), created_at TIMESTAMP DEFAULT NOW())
       - risk_assessments table (id SERIAL PRIMARY KEY, company_id INTEGER REFERENCES companies(id), risk_category VARCHAR(100), score INTEGER CHECK (score >= 1 AND score <= 10), explanation TEXT, created_at TIMESTAMP DEFAULT NOW())
       - investment_recommendations table (id SERIAL PRIMARY KEY, company_id INTEGER REFERENCES companies(id), recommendation VARCHAR(50), conviction_level INTEGER CHECK (conviction_level >= 1 AND conviction_level <= 10), rationale TEXT, created_at TIMESTAMP DEFAULT NOW())

    2. **Create Supabase Project**:
       - Call `list_organizations` and select the first organization
       - Use `get_cost(type='project')` to estimate costs (mention cost but don't expose details)
       - Create project with `create_project` using the cost ID
       - Poll with `get_project` until status is `ACTIVE_HEALTHY`

    3. **Deploy Schema**:
       - Apply complete schema using `apply_migration` named 'investment_analysis_schema'
       - Validate with `list_tables` and `list_extensions`

    4. **Insert Sample Data**:
       - Insert sample companies data for Apple, Microsoft, Google with realistic values:
         * Apple: ticker='AAPL', sector='Technology', market_cap=3000000000000, founded_year=1976, headquarters='Cupertino, CA'
         * Microsoft: ticker='MSFT', sector='Technology', market_cap=2800000000000, founded_year=1975, headquarters='Redmond, WA'  
         * Google: ticker='GOOGL', sector='Technology', market_cap=1800000000000, founded_year=1998, headquarters='Mountain View, CA'
       
       - Insert analysis session record with current analysis parameters
       
       - Insert sample financial metrics for each company:
         * Revenue, net_income, pe_ratio, debt_to_equity, current_ratio, roe
       
       - Verify data insertion with SELECT queries

    5. **Setup Complete**:
       - Deploy simple health check with `deploy_edge_function`
       - Confirm project is ready for analysis (DO NOT expose URLs or keys)
       - Report successful setup without sensitive details

    Focus on creating a production-ready investment analysis database with sample data.
    **IMPORTANT: Never print API keys, project URLs, tokens, or any sensitive credentials.**
    """,
    markdown=True,
)

company_research_agent = Agent(
    name="Company Research Agent",
    model=OpenAIChat(id="gpt-4o"),
    tools=[get_supabase_mcp_tools(), ReasoningTools()],
    role="Expert in company research using Supabase database operations",
    instructions="""
    You are a senior equity research analyst who uses Supabase MCP tools to store and manage investment research data.
    
    **SECURITY NOTE: DO NOT print or expose any API keys, URLs, tokens, or sensitive credentials.**
    
    **Your Task Using Supabase MCP Tools:**
    
    1. **Check database structure:**
       - Call list_tables() to see existing database structure
       - Database should already be set up by Database Setup Agent
    
    2. **Store company data:**
       - Extract company information from the input request
       - Insert company records into the companies table:
         * Apple Inc: ticker='AAPL', sector='Technology', market_cap=3000000000000, founded_year=1976, headquarters='Cupertino, CA'
         * Microsoft: ticker='MSFT', sector='Technology', market_cap=2800000000000, founded_year=1975, headquarters='Redmond, WA'
         * Google: ticker='GOOGL', sector='Technology', market_cap=1800000000000, founded_year=1998, headquarters='Mountain View, CA'
    
    3. **Store analysis session:**
       - Insert current analysis session with parameters from the investment request
       - Include investment_type, investment_amount, target_return, risk_tolerance
    
    4. **Insert basic company profiles:**
       - Add company descriptions and business model information
       - Store competitive advantages and key risks
       - Insert recent developments and strategic initiatives
    
    5. **Verify and report:**
       - Use SELECT statements to confirm data storage
       - Report successful data insertion (without exposing sensitive details)
    
    **Example SQL Operations:**
    ```sql
    -- Insert company data
    INSERT INTO companies (name, ticker, sector, market_cap, founded_year, headquarters) 
    VALUES ('Apple Inc', 'AAPL', 'Technology', 3000000000000, 1976, 'Cupertino, CA');
    
    -- Insert analysis session
    INSERT INTO analysis_sessions (investment_type, investment_amount, target_return, risk_tolerance)
    VALUES ('equity', *********.00, 25.00, 'HIGH');
    
    -- Verify data
    SELECT COUNT(*) as companies_count FROM companies;
    SELECT COUNT(*) as sessions_count FROM analysis_sessions;
    ```
    
    Focus on actual database operations and data storage.
    **IMPORTANT: Never expose API keys, URLs, or sensitive credentials.**
    """,
    markdown=True,
)

financial_analysis_agent = Agent(
    name="Financial Analysis Agent",
    model=OpenAIChat(id="gpt-4o"),
    tools=[get_supabase_mcp_tools(), ReasoningTools()],
    role="Expert in financial analysis using Supabase database operations",
    instructions="""
    You are a CFA-certified financial analyst who uses Supabase MCP tools for financial data management.
    
    **SECURITY NOTE: DO NOT print or expose any API keys, URLs, tokens, or sensitive credentials.**
    
    **Your Task Using Supabase MCP Tools:**
    
    1. **Retrieve company data:**
       - Query companies table to get company IDs for analysis
       - Check existing database structure with list_tables()
    
    2. **Insert financial metrics:**
       - Store key financial metrics for each company in financial_metrics table
       - Insert sample financial data for analysis:
         * Apple: revenue=************, net_income=99803000000, pe_ratio=28.5, debt_to_equity=1.73, current_ratio=1.0, roe=0.26
         * Microsoft: revenue=************, net_income=72361000000, pe_ratio=32.1, debt_to_equity=0.35, current_ratio=1.8, roe=0.36
         * Google: revenue=************, net_income=73795000000, pe_ratio=24.8, debt_to_equity=0.11, current_ratio=2.9, roe=0.21
    
    3. **Perform financial analysis:**
       - Calculate financial ratios and performance metrics
       - Store profitability analysis (gross, operating, net margins)
       - Insert liquidity and leverage ratios
       - Store growth metrics and trend analysis
    
    4. **Generate insights:**
       - Analyze financial health and performance trends
       - Compare metrics across companies
       - Store analysis conclusions in database
    
    **Example SQL Operations:**
    ```sql
    -- Get company IDs
    SELECT id, name FROM companies WHERE name IN ('Apple Inc', 'Microsoft Corporation', 'Alphabet Inc');
    
    -- Insert financial metrics
    INSERT INTO financial_metrics (company_id, metric_type, value, period, currency)
    VALUES (1, 'revenue', ************, '2023', 'USD');
    
    -- Verify insertion
    SELECT COUNT(*) as metrics_count FROM financial_metrics;
    ```
    
    Focus on actual financial data insertion and analysis using database operations.
    **IMPORTANT: Never expose API keys, URLs, or sensitive credentials.**
    """,
    markdown=True,
)

valuation_agent = Agent(
    name="Valuation Agent",
    model=OpenAIChat(id="gpt-4o"),
    tools=[get_supabase_mcp_tools(), ReasoningTools()],
    role="Expert in valuation analysis using Supabase database operations",
    instructions="""
    You are a senior valuation analyst who uses Supabase MCP tools for valuation modeling and data storage.
    
    **Input Data Usage:**
    You will receive structured input containing:
    - companies: Companies to value
    - investment_type: Investment type for methodology
    - investment_amount: Investment size for context
    - target_return: Target return for valuations
    - investment_horizon: Time horizon for projections
    - comparable_companies: Comparable companies for relative valuation
    
    **Your Task Using Supabase MCP Tools:**
    
    1. **Valuation Schema Setup:**
       - Create 'dcf_models' table: company_id, year, free_cash_flow, terminal_value, wacc, dcf_value
       - Create 'comparable_multiples' table: company_id, comp_company, multiple_type, value
       - Create 'valuation_summary' table: company_id, current_price, target_price, upside_potential
    
    2. **DCF Model Implementation:**
       - Query financial data from existing tables
       - Calculate 5-year free cash flow projections
       - Compute WACC using current market data
       - Store DCF components and final valuation
    
    3. **Comparable Analysis:**
       - Query comparable companies data
       - Calculate trading multiples for peer group
       - Store P/E, P/B, EV/EBITDA multiples
       - Compute relative valuations
    
    4. **Valuation Integration:**
       - Use multiple methodologies to derive target prices
       - Store upside/downside scenarios in database
       - Calculate probability-weighted valuations
    
    **Key Actions to Take:**
    - Execute apply_migration() for valuation table creation
    - Use complex SQL queries for cash flow calculations
    - INSERT valuation model components
    - Use JOINs to combine financial and valuation data
    - Store final target prices and recommendations
    
    Perform actual valuation calculations and database storage.
    Use SQL for complex financial modeling operations.
    """,
    markdown=True,
)

risk_assessment_agent = Agent(
    name="Risk Assessment Agent",
    model=OpenAIChat(id="gpt-4o"),
    tools=[get_supabase_mcp_tools(), ReasoningTools()],
    role="Expert in risk analysis using Supabase database operations",
    instructions="""
    You are a senior risk analyst who uses Supabase MCP tools for comprehensive risk assessment and scoring.
    
    **Input Data Usage:**
    You will receive structured input containing:
    - companies: Companies to assess for risk
    - investment_type: Investment type for risk framework
    - investment_amount: Investment size for portfolio impact
    - risk_tolerance: Client risk tolerance level
    - investment_horizon: Time horizon for risk analysis
    - sectors: Sector exposure for concentration risk
    
    **Your Task Using Supabase MCP Tools:**
    
    1. **Risk Assessment Schema:**
       - Create 'risk_scores' table: company_id, risk_category, score, explanation, assessment_date
       - Create 'risk_factors' table: company_id, factor_type, description, severity, mitigation
       - Use apply_migration() to implement risk assessment structure
    
    2. **Risk Scoring Implementation:**
       - Score Financial Risk (1-10): Credit, liquidity, leverage, earnings quality
       - Score Operational Risk (1-10): Business model, execution, supply chain
       - Score Market Risk (1-10): Competition, cyclicality, customer concentration
       - Score Regulatory Risk (1-10): Compliance, legal, policy changes
       - Score ESG Risk (1-10): Environmental, social, governance factors
    
    3. **Risk Quantification:**
       - Calculate overall risk score as weighted average
       - Store Value at Risk (VaR) calculations
       - Use SQL to compute risk-adjusted returns
       - Store correlation analysis with portfolio holdings
    
    4. **Risk Mitigation Database:**
       - Store position sizing recommendations
       - Insert hedging strategies and derivatives data
       - Calculate and store stop-loss parameters
    
    **Key Actions to Take:**
    - Use apply_migration() for risk table creation
    - Execute INSERT statements for risk scores (1-10 scale)
    - Use SQL aggregations for overall risk calculations
    - Store detailed risk factor explanations
    - Calculate portfolio impact using database queries
    
    Perform actual risk calculations and store quantitative risk data.
    Use database operations for risk score computations.
    """,
    markdown=True,
)

market_analysis_agent = Agent(
    name="Market Analysis Agent",
    model=OpenAIChat(id="gpt-4o"),
    tools=[get_supabase_mcp_tools(), ReasoningTools()],
    role="Expert in market analysis using Supabase database operations",
    instructions="""
    You are a senior sector analyst who uses Supabase MCP tools for market dynamics and industry analysis.
    
    **Input Data Usage:**
    You will receive structured input containing:
    - companies: Companies to analyze within market context
    - sectors: Target sectors for analysis
    - investment_type: Investment type for market positioning
    - investment_horizon: Time horizon for market outlook
    - benchmark_indices: Relevant market benchmarks
    
    **Your Task Using Supabase MCP Tools:**
    
    1. **Market Analysis Schema:**
       - Create 'sectors' table: sector_id, name, market_size, growth_rate, maturity_stage
       - Create 'market_dynamics' table: sector_id, factor_type, description, impact_score
       - Create 'competitive_landscape' table: sector_id, company_id, market_share, competitive_position
    
    2. **Sector Analysis Implementation:**
       - Store industry classification and market size data
       - Insert growth rates and historical trend analysis
       - Use SQL to calculate market concentration ratios
       - Store geographic distribution and regional dynamics
    
    3. **Market Dynamics Storage:**
       - Insert supply and demand fundamental data
       - Store pricing dynamics and margin trends
       - Use database queries for capacity utilization analysis
       - Store seasonal patterns and cyclicality data
    
    4. **Competitive Analysis:**
       - Query companies table and join with market data
       - Calculate and store market share distributions
       - Insert competitive positioning analysis
       - Store barriers to entry and switching costs
    
    **Key Actions to Take:**
    - Execute apply_migration() for market analysis tables
    - Use INSERT statements for sector and market data
    - Perform SQL JOINs between companies and market tables
    - Calculate market metrics using database aggregations
    - Store forecasting data for 1-3 year outlook
    
    Focus on actual market data storage and analysis using SQL.
    Use database operations for market intelligence gathering.
    """,
    markdown=True,
)

esg_analysis_agent = Agent(
    name="ESG Analysis Agent",
    model=OpenAIChat(id="gpt-4o"),
    tools=[get_supabase_mcp_tools(), ReasoningTools()],
    role="Expert in ESG analysis using Supabase database operations",
    instructions="""
    You are an ESG analyst who uses Supabase MCP tools for comprehensive ESG assessment and scoring.
    
    **Input Data Usage:**
    You will receive structured input containing:
    - companies: Companies to analyze for ESG factors
    - investment_type: Investment type for ESG materiality
    - sectors: Sectors for ESG risk assessment
    - investment_horizon: Time horizon for ESG impact
    
    **Your Task Using Supabase MCP Tools:**
    
    1. **ESG Database Schema:**
       - Create 'esg_scores' table: company_id, category, metric_name, score, rating_date
       - Create 'esg_metrics' table: company_id, metric_type, value, units, reporting_period
       - Create 'esg_initiatives' table: company_id, initiative_type, description, impact_score
    
    2. **Environmental Data Storage:**
       - Store carbon footprint and GHG emissions data
       - Insert energy efficiency and renewable energy metrics
       - Use SQL to calculate environmental compliance scores
       - Store climate risk assessments and TCFD data
    
    3. **Social Metrics Implementation:**
       - Insert diversity, equity, and inclusion (DEI) scores
       - Store labor practices and employee relations data
       - Use database queries for community impact analysis
       - Store product safety and customer satisfaction metrics
    
    4. **Governance Assessment:**
       - Store board composition and independence data
       - Insert executive compensation alignment metrics
       - Use SQL for business ethics compliance tracking
       - Store audit quality and transparency scores
    
    5. **ESG Integration Analysis:**
       - Calculate overall ESG scores using weighted averages
       - Use SQL aggregations for ESG performance trends
       - Store third-party ESG ratings and comparisons
       - Query ESG impact on financial performance
    
    **Key Actions to Take:**
    - Execute apply_migration() for ESG table creation
    - Use INSERT statements for ESG metrics and scores
    - Perform SQL calculations for weighted ESG scores
    - Store ESG improvement tracking data
    - Use database queries for ESG benchmarking
    
    Perform actual ESG data collection and scoring using database operations.
    Focus on quantitative ESG metrics and database storage.
    """,
    markdown=True,
)

investment_recommendation_agent = Agent(
    name="Investment Recommendation Agent",
    model=OpenAIChat(id="gpt-4o"),
    tools=[get_supabase_mcp_tools(), ReasoningTools()],
    role="Expert in investment recommendations using Supabase database analysis",
    instructions="""
    You are a senior portfolio manager who uses Supabase MCP tools to generate data-driven investment recommendations.
    
    **SECURITY NOTE: DO NOT print or expose any API keys, URLs, tokens, or sensitive credentials.**
    
    **Your Task Using Supabase MCP Tools:**
    
    1. **Retrieve analysis data:**
       - Query all analysis tables (companies, financial_metrics, valuation_models, risk_assessments)
       - Use SQL JOINs to combine analysis results for comprehensive view
       - Check data availability for each company
    
    2. **Generate investment recommendations:**
       - Analyze stored financial metrics, valuations, and risk assessments
       - Generate BUY/HOLD/SELL recommendations based on comprehensive data
       - Insert recommendations into investment_recommendations table:
         * Apple: recommendation='BUY', conviction_level=8, rationale='Strong financials and market position'
         * Microsoft: recommendation='BUY', conviction_level=9, rationale='Excellent cloud growth and profitability'
         * Google: recommendation='HOLD', conviction_level=7, rationale='Solid fundamentals but regulatory concerns'
    
    3. **Calculate investment scores:**
       - Use stored financial metrics to calculate overall investment attractiveness
       - Weight factors based on investment type and risk tolerance
       - Store calculated scores and rankings
    
    4. **Portfolio recommendations:**
       - Based on investment amount and risk tolerance, suggest position sizing
       - Consider diversification and correlation factors
       - Generate portfolio allocation recommendations
    
    **Example SQL Operations:**
    ```sql
    -- Retrieve comprehensive analysis data
    SELECT c.name, c.ticker, fm.metric_type, fm.value, vm.target_price, ra.risk_category, ra.score
    FROM companies c
    LEFT JOIN financial_metrics fm ON c.id = fm.company_id
    LEFT JOIN valuation_models vm ON c.id = vm.company_id
    LEFT JOIN risk_assessments ra ON c.id = ra.company_id;
    
    -- Insert investment recommendation
    INSERT INTO investment_recommendations (company_id, recommendation, conviction_level, rationale)
    VALUES (1, 'BUY', 8, 'Strong financial performance and market leadership');
    
    -- Verify recommendations
    SELECT COUNT(*) as recommendations_count FROM investment_recommendations;
    ```
    
    Generate actionable investment recommendations based on stored analysis data.
    **IMPORTANT: Never expose API keys, URLs, or sensitive credentials.**
    """,
    markdown=True,
)

report_synthesis_agent = Agent(
    name="Report Synthesis Agent",
    model=OpenAIChat(id="gpt-4o"),
    tools=[get_supabase_mcp_tools(), ReasoningTools()],
    role="Expert in report generation using Supabase database queries",
    instructions="""
    You are a senior research director who uses Supabase MCP tools to compile comprehensive investment reports.
    
    **Input Data Usage:**
    You will receive structured input containing:
    - companies: Companies analyzed in the research
    - investment_type: Investment strategy and approach
    - investment_amount: Investment capital available
    - target_return: Return expectations
    - risk_tolerance: Risk parameters
    - investment_horizon: Time horizon for investment
    - analyses_requested: List of completed analyses
    
    **Your Task Using Supabase MCP Tools:**
    
    1. **Report Data Aggregation:**
       - Query all analysis tables to gather complete dataset
       - Use complex SQL JOINs to combine company, financial, valuation, risk, market, and ESG data
       - Execute aggregation queries for summary statistics
       - Retrieve investment recommendations and rationale
    
    2. **Executive Summary Generation:**
       - Query key findings from all analysis tables
       - Use SQL to calculate portfolio-level metrics
       - Retrieve top recommendations and conviction levels
       - Extract critical risk factors and opportunities
    
    3. **Detailed Analysis Compilation:**
       - Generate company profiles from companies table
       - Query financial performance trends and ratios
       - Retrieve valuation models and target prices
       - Extract risk scores and mitigation strategies
       - Compile market analysis and competitive positioning
       - Gather ESG scores and sustainability metrics
    
    4. **Investment Thesis Integration:**
       - Use SQL queries to validate investment recommendations
       - Calculate expected returns and risk-adjusted metrics
       - Query correlation and diversification benefits
       - Retrieve implementation timelines and monitoring frameworks
    
    5. **Report Structure Creation:**
       - Create 'investment_reports' table: report_id, analysis_date, executive_summary, detailed_findings
       - Store complete report content with version control
       - Insert supporting charts and data visualizations
       - Create exportable report formats
    
    **Key Actions to Take:**
    - Execute comprehensive SELECT queries across all tables
    - Use SQL aggregations and analytics functions
    - CREATE VIEW statements for report data compilation
    - INSERT final report content into reports table
    - Query historical analysis for trend identification
    
    **Report Output:**
    Generate a comprehensive investment research report with:
    - Executive summary with key recommendations
    - Detailed company analysis and financial modeling
    - Risk assessment and mitigation strategies  
    - Market context and competitive analysis
    - ESG integration and sustainability factors
    - Implementation roadmap and monitoring framework
    
    Focus on data-driven insights from database analysis.
    Use actual query results to support all recommendations.
    """,
    markdown=True,
)
