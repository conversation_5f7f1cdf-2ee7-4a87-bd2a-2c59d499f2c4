from typing import Dict, List

from agno.agent import Agent
from agno.models.anthropic import <PERSON>
from pydantic import BaseModel, Field


class MovieScript(BaseModel):
    setting: str = Field(
        ..., description="Provide a nice setting for a blockbuster movie."
    )
    ending: str = Field(
        ...,
        description="Ending of the movie. If not available, provide a happy ending.",
    )
    genre: str = Field(
        ...,
        description="Genre of the movie. If not available, select action, thriller or romantic comedy.",
    )
    name: str = Field(..., description="Give a name to this movie")
    characters: List[str] = Field(..., description="Name of characters for this movie.")
    storyline: str = Field(
        ..., description="3 sentence storyline for the movie. Make it exciting!"
    )
    rating: Dict[str, int] = Field(
        ...,
        description="Your own rating of the movie. 1-10. Return a dictionary with the keys 'story' and 'acting'.",
    )


# Agent that uses structured outputs
structured_output_agent = Agent(
    model=<PERSON>(id="claude-sonnet-4-20250514"),
    description="You write movie scripts.",
    response_model=MovieScript,
)

structured_output_agent.print_response(
    "New York", stream=True, stream_intermediate_steps=True
)
