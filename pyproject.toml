[project]
name = "epidemiology-research-team"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "agno>=1.8.1",
    "ddgs>=9.5.5",
    "duckduckgo-search>=8.1.1",
    "fastapi[standard]>=0.116.1",
    "googlesearch-python>=1.3.0",
    "lxml-html-clean>=0.4.2",
    "newspaper4k>=*******",
    "numpy>=2.3.2",
    "openai>=1.102.0",
    "pandas>=2.3.2",
    "plotly>=6.3.0",
    "pycountry>=24.6.1",
    "sqlalchemy>=2.0.43",
    "streamlit>=1.49.1",
    "scipy>=1.11.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "openpyxl>=3.1.0",
    "reportlab>=4.0.0",
    "jinja2>=3.1.0",
]
